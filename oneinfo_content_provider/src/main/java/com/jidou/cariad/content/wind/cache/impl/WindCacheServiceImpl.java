package com.jidou.cariad.content.wind.cache.impl;

import com.alibaba.fastjson.JSON;
import com.jidou.cariad.content.common.content.RedisContent;
import com.jidou.cariad.content.common.cp.NmcUtil;
import com.jidou.cariad.content.common.utils.RedisUtil;
import com.jidou.cariad.content.model.vo.weather.WeatherHourlyVO;
import com.jidou.cariad.content.service.WeatherAlertService;
import com.jidou.cariad.content.service.impl.WeatherServiceImpl;
import com.jidou.cariad.content.wind.alert.UnscheduledCityAlertManager;
import com.jidou.cariad.content.wind.cache.WindCacheService;
import com.jidou.cariad.content.wind.cache.validation.CacheValidationService;
import com.jidou.cariad.content.wind.dto.FallbackResult;
import com.jidou.cariad.content.wind.dto.RequestInfo;
import com.jidou.cariad.content.wind.dto.WindCacheData;
import com.jidou.cariad.content.wind.dto.WindCacheMetadata;
import com.jidou.cariad.content.wind.dto.WindData;
import com.jidou.cariad.content.wind.parser.WeatherHtmlParser;
import com.jidou.cariad.content.wind.schedule.CityScheduleCacheService;
import com.jidou.cariad.content.wind.schedule.CityScheduleFallbackStrategy;
import com.jidou.cariad.content.wind.util.WindUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 风速缓存服务实现类
 * 作为核心协调器，处理用户请求并协调各个组件
 * 专注于风速数据的缓存管理和更新策略
 *
 * <AUTHOR>
 * @since 2024/12/19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WindCacheServiceImpl implements WindCacheService {

    private final RedisUtil redisUtil;
    private final NmcUtil nmcUtil;
    private final WeatherHtmlParser weatherHtmlParser;
    private final CacheValidationService cacheValidationService;
    private final CityScheduleCacheService cityScheduleCacheService;
    private final UnscheduledCityAlertManager unscheduledCityAlertManager;
    private final CityScheduleFallbackStrategy cityScheduleFallbackStrategy;
    private final WeatherAlertService weatherAlertService;

    @Value("${weather.wind.response-limit:3}")
    private int windResponseLimit;
    @Value("${weather.wind.schedule-limit:3}")
    private int windScheduleLimit;

    /**
     * 为逐小时天气数据设置风速信息
     * 这是核心的用户请求处理逻辑，区分计划内和计划外城市
     */
    @Override
    public void setHourlyWindSpeed(List<WeatherHourlyVO> hourly) {
        if (hourly == null || hourly.isEmpty()) {
            return;
        }

        String locationPath = hourly.get(0).getLocation().getPath();
        log.debug("开始为{}地区设置风速数据", locationPath);

        long startTime = System.currentTimeMillis();

        try {
            // 获取现有缓存数据
            WindCacheData existingCache = getWindCacheData(locationPath);

            // 使用智能缓存验证服务判断缓存有效性
            if (cacheValidationService.isWindCacheValid(locationPath, existingCache)) {
                log.debug("城市{}风速缓存有效，直接使用缓存数据", locationPath);
                processWindCache(hourly, existingCache);
                return;
            }

            // 缓存无效，检查城市调度状态并执行相应策略
            if (cityScheduleCacheService.isScheduledCity(locationPath)) {
                handleScheduledCityRequest(hourly, locationPath, existingCache);
            } else {
                handleUnscheduledCityRequest(hourly, locationPath, startTime);
            }

        } catch (Exception e) {
            log.error("处理城市{}风速数据时发生异常: {}", locationPath, e.getMessage(), e);
            // 异常降级处理：使用空风速数据，避免影响天气服务
            setEmptyWindData(hourly);
        }
    }

    /**
     * 处理计划内城市请求
     * 直接调用降级策略，不再使用反射
     */
    private void handleScheduledCityRequest(List<WeatherHourlyVO> hourly, String locationPath,
                                            WindCacheData existingCache) {
        log.info("计划内城市{}缓存无效，执行降级策略", locationPath);

        try {
            // 调用降级策略，传入已有缓存数据避免重复调用
            FallbackResult fallbackResult = cityScheduleFallbackStrategy.handleScheduledCityFailure(locationPath, existingCache);

            if (fallbackResult != null && fallbackResult.getFallbackData() != null) {
                // 处理降级数据
                processWindCache(hourly, fallbackResult.getFallbackData());

                // 根据降级结果的建议执行后续操作
                handleFallbackActions(locationPath, fallbackResult);
                return;
            }
        } catch (Exception e) {
            log.warn("调用降级策略失败: {}", e.getMessage());
        }

        // 降级策略也失败时，使用最基础的空数据
        log.error("降级策略执行失败，使用基础空数据");
        WindCacheData emptyData = createBasicEmptyData(locationPath);
        processWindCache(hourly, emptyData);
    }

    /**
     * 处理降级结果的后续操作
     */
    private void handleFallbackActions(String locationPath, FallbackResult fallbackResult) {
        try {
            // 只处理预警，不处理调度重置
            if (fallbackResult.isNeedsAlert() && fallbackResult.getAlertMessage() != null) {
                weatherAlertService.sendDataAlert(
                        "计划内城市缓存异常",
                        fallbackResult.getAlertMessage(),
                        locationPath
                );
                log.info("已发送计划内城市{}的缓存异常预警", locationPath);
            }

            // 调度重置由运维或定时任务处理，不在用户请求路径中处理
            if (fallbackResult.isNeedsReschedule()) {
                log.warn("计划内城市{}需要重置调度时间，请运维人员检查或等待下次定时任务处理", locationPath);
            }

            if (fallbackResult.isNeedsSyncUpdate()) {
                log.info("计划内城市{}需要更新数据，请运维人员检查或等待下次定时任务处理", locationPath);
                updateWindSpeedDataForUnscheduledCity(locationPath);
            }

        } catch (Exception e) {
            log.error("处理降级后续操作失败，城市: {}, 错误: {}", locationPath, e.getMessage(), e);
        }
    }

    /**
     * 创建基础空数据
     */
    private WindCacheData createBasicEmptyData(String locationPath) {
        return WindCacheData.builder()
                .locationPath(locationPath)
                .dataSource("basic-empty-fallback")
                .lastUpdated(LocalDateTime.now())
                .windData(Collections.emptyList())
                .version("fallback-basic")
                .build();
    }

    /**
     * 处理计划外城市请求
     * 计划外城市需要实时获取数据，同时进行预警管理
     */
    private void handleUnscheduledCityRequest(List<WeatherHourlyVO> hourly, String locationPath, long startTime) {
        log.info("计划外城市{}需要实时获取数据", locationPath);

        try {
            // 同步获取风速数据（将原来的异步改为同步）
            WindCacheData newData = updateWindSpeedDataForUnscheduledCity(locationPath);

            if (newData != null && newData.hasValidData()) {
                processWindCache(hourly, newData);
                log.info("计划外城市{}数据获取成功", locationPath);
            } else {
                log.warn("计划外城市{}数据获取失败，使用空风速数据", locationPath);
                setEmptyWindData(hourly);
            }

            // 记录请求信息并发送预警
            long responseTime = System.currentTimeMillis() - startTime;
            RequestInfo requestInfo = RequestInfo.builder()
                    .requestTime(LocalDateTime.now())
                    .responseTime(responseTime)
                    .remoteIp(getRemoteIp()) // 可以从ThreadLocal获取
                    .userAgent(getUserAgent()) // 可以从ThreadLocal获取
                    .build();

            unscheduledCityAlertManager.sendUnscheduledCityAlert(locationPath, requestInfo);

        } catch (Exception e) {
            log.error("处理计划外城市{}请求时发生异常: {}", locationPath, e.getMessage(), e);
            setEmptyWindData(hourly);
        }
    }

    /**
     * 为计划外城市更新风速数据
     *
     * <p>同步执行，快速响应用户请求。特点：
     * <ul>
     *   <li>缓存时间：24小时（计划外城市短期缓存）</li>
     *   <li>最大延迟：10秒（避免用户等待过久）</li>
     *   <li>锁定时间：2分钟</li>
     *   <li>数据源标记：nmc-sync</li>
     * </ul>
     *
     * @param locationPath 城市路径，如 "beijing/beijing"
     * @return 风速缓存数据，获取失败时返回null
     * @since 2.0
     */
    private WindCacheData updateWindSpeedDataForUnscheduledCity(String locationPath) {
        String cacheKey = RedisContent.getWindKey(RedisContent.WEATHER_WIND_HOURLY_PREFIX, locationPath);
        String cityLockKey = RedisContent.WEATHER_WIND_LOCK_PREFIX + locationPath;
        String responseLimitKey = RedisContent.getWindKeyWithDate(RedisContent.WEATHER_WIND_RESPONSE_LIMIT_PREFIX,
                                                                  "global");

        Boolean lockAcquired = false;
        try {
            // 1. 尝试获取城市锁（缩短锁定时间到2分钟）
            lockAcquired = redisUtil.setIfNotExists(cityLockKey, "1", 2L, TimeUnit.MINUTES);
            if (!lockAcquired) {
                log.info("城市{}的数据正在被其他线程处理，跳过本次更新", locationPath);
                return null;
            }

            // 2. 检查响应限流
            RedisUtil.RequestDelay responseRequestDelay = redisUtil.allowRequestWithDelay(
                    responseLimitKey, windResponseLimit, 600, TimeUnit.SECONDS, 1, 300);

            if (!responseRequestDelay.isAllowed()) {
                log.warn("响应限流已达上限，计划外城市{}请求被限流", locationPath);
                return null;
            }

            // 3. 延迟处理（为同步请求缩短延迟时间）
            if (responseRequestDelay.getDelay() > 0) {
                long maxDelay = 10; // 最大延迟10秒，避免用户等待过久
                long actualDelay = Math.min(responseRequestDelay.getDelay(), maxDelay);
                log.info("城市:{},请求将在{}秒后执行", locationPath, actualDelay);
                Thread.sleep(actualDelay * 1000);
            }

            // 4. 获取气象局数据（利用两层缓存优化）
            String url = nmcUtil.buildWeatherUrl(locationPath); // 第一层缓存：locationPath -> URL
            List<WindData> newData = weatherHtmlParser.getHourlyWindSpeed(url); // 第二层缓存：URL -> WindData

            if (newData.isEmpty()) {
                log.warn("从气象局获取的风速数据为空，locationPath: {}", locationPath);
                return null;
            }

            // 5. 创建轻量级元数据缓存（不包含实际风速数据，避免重复缓存）
            WindCacheMetadata metadata = WindCacheMetadata.builder()
                    .lastUpdated(LocalDateTime.now())
                    .dataSource("nmc-sync")
                    .version("2.0")
                    .locationPath(locationPath)
                    .dataCount(newData.size())
                    .build();

            // 6. 缓存轻量级元数据，缓存时间1小时（实际数据由@MyCache管理）
            redisUtil.setString(cacheKey, JSON.toJSONString(metadata),
                                TimeUnit.HOURS.toSeconds(1));

            // 7. 组装完整的WindCacheData返回（不缓存，仅用于返回）
            WindCacheData cacheData = metadata.toWindCacheData(newData);

            log.info("为计划外城市{}同步更新风速数据成功，共{}条数据", locationPath, newData.size());
            return cacheData;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("为计划外城市{}同步更新风速数据被中断: {}", locationPath, e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("为计划外城市{}同步更新风速数据失败: {}", locationPath, e.getMessage(), e);
            return null;
        } finally {
            // 释放锁
            if (lockAcquired) {
                redisUtil.delKey(cityLockKey);
                log.debug("已释放城市{}的处理锁", locationPath);
            }
        }
    }

    /**
     * 处理风速缓存数据
     */
    private void processWindCache(List<WeatherHourlyVO> hourly, WindCacheData cacheData) {
        if (cacheData != null && cacheData.hasValidData()) {
            WeatherServiceImpl.processWindCache(hourly, cacheData.getWindData(), cacheData.getLocationPath());
        } else {
            setEmptyWindData(hourly);
        }
    }

    /**
     * 设置空风速数据
     */
    private void setEmptyWindData(List<WeatherHourlyVO> hourly) {
        // 设置默认风速数据，避免返回null
        for (WeatherHourlyVO hour : hourly) {
            if (hour.getWindSpeed() == null) {
                hour.setWindSpeed("0");
            }
            if (hour.getWindDirection() == null) {
                hour.setWindDirection("无风");
            }
            if (hour.getWindScale() == null) {
                hour.setWindScale("0级");
            }
            hour.setWindScale(WindUtils.mapWindSpeedToScale(hour.getWindSpeed()));
        }
    }

    // 以下方法用于获取请求上下文信息（可以根据实际框架实现）
    private String getRemoteIp() {
        // 这里可以从ThreadLocal或Request上下文获取
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        return request.getRemoteAddr();
    }

    private String getUserAgent() {
        // 这里可以从ThreadLocal或Request上下文获取
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        return request.getHeader("User-Agent");
    }

    @Override
    public WindCacheData getWindCacheData(String locationPath) {
        try {
            // 1. 从缓存获取元数据
            String cacheKey = RedisContent.getWindKey(RedisContent.WEATHER_WIND_HOURLY_PREFIX, locationPath);
            String cachedMetadataStr = redisUtil.getString(cacheKey);

            if (cachedMetadataStr != null) {
                WindCacheMetadata metadata = JSON.parseObject(cachedMetadataStr, WindCacheMetadata.class);

                // 2. 获取实际的风速数据（通过@MyCache缓存）
                String url = nmcUtil.buildWeatherUrl(locationPath);
                List<WindData> windData = weatherHtmlParser.getHourlyWindSpeed(url);

                // 3. 组装完整的WindCacheData
                return metadata.toWindCacheData(windData);
            }
        } catch (Exception e) {
            log.warn("解析风速缓存数据失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 为计划内城市更新风速数据
     *
     * <p>用于定时任务的数据维护。特点：
     * <ul>
     *   <li>缓存时间：6天（计划内城市长期缓存）</li>
     *   <li>无限流检查：调度任务已在上层进行限流控制</li>
     *   <li>锁定时间：5分钟</li>
     *   <li>数据源标记：nmc</li>
     *   <li>直接执行：不加入队列，避免重复处理</li>
     * </ul>
     *
     * @param locationPath 城市路径，如 "beijing/beijing"
     * @since 2.0
     */
    @Override
    public void updateWindSpeedDataForScheduledCity(String locationPath) {
        String cacheKey = RedisContent.getWindKey(RedisContent.WEATHER_WIND_HOURLY_PREFIX, locationPath);
        String cityLockKey = RedisContent.WEATHER_WIND_LOCK_PREFIX + locationPath;

        Boolean lockAcquired = false;
        try {
            // 1. 尝试获取城市锁
            lockAcquired = redisUtil.setIfNotExists(cityLockKey, "1", 5L, TimeUnit.MINUTES);
            if (!lockAcquired) {
                log.info("城市{}的数据正在被其他线程处理，跳过本次更新", locationPath);
                return;
            }

            // 2. 直接执行数据更新（调度任务已在上层进行限流控制）

            // 3. 获取气象局数据（利用两层缓存优化）
            String url = nmcUtil.buildWeatherUrl(locationPath); // 第一层缓存：locationPath -> URL
            List<WindData> newData = weatherHtmlParser.getHourlyWindSpeed(url); // 第二层缓存：URL -> WindData

            if (newData.isEmpty()) {
                log.warn("从气象局获取的风速数据为空，locationPath: {}", locationPath);
                return;
            }

            // 4. 创建轻量级元数据缓存（不包含实际风速数据，避免重复缓存）
            WindCacheMetadata metadata = WindCacheMetadata.builder()
                    .lastUpdated(LocalDateTime.now())
                    .dataSource("nmc-schedule")
                    .version("2.0")
                    .locationPath(locationPath)
                    .dataCount(newData.size())
                    .build();

            // 5. 缓存轻量级元数据，缓存时间6小时（实际数据由@MyCache管理）
            redisUtil.setString(cacheKey, JSON.toJSONString(metadata),
                                TimeUnit.HOURS.toSeconds(6));

            log.info("为计划内城市{}更新风速数据成功，共{}条数据", locationPath, newData.size());

        } catch (Exception e) {
            log.error("为计划内城市{}更新风速数据失败: {}", locationPath, e.getMessage(), e);
            handleWindSpeedUpdateFailure(locationPath, cacheKey);
        } finally {
            // 释放锁
            if (lockAcquired) {
                redisUtil.delKey(cityLockKey);
                log.debug("已释放城市{}的处理锁", locationPath);
            }
        }
    }

    @Override
    public boolean shouldUpdateCache(WindCacheData cacheData) {
        if (cacheData == null) {
            return true;
        }

        // 1. 数据超过24小时未更新
        if (cacheData.isExpired(24)) {
            return true;
        }

        // 2. 检查数据完整性：是否包含未来2天的数据
        if (!hasCompleteFutureData(cacheData.getWindData())) {
            return true;
        }

        // 3. 如果是今天获取的完整数据且不超过24小时，不需要更新
        return false;
    }

    /**
     * 获取更新原因描述
     */
    private String getUpdateReason(WindCacheData cacheData) {
        if (cacheData.isExpired(6)) {
            return "数据过期";
        }
        if (!hasCompleteFutureData(cacheData.getWindData())) {
            return "数据不完整";
        }
        if (!cacheData.isTodayData()) {
            return "非今日数据";
        }
        return "常规更新";
    }

    /**
     * 检查是否有完整的未来数据
     */
    private boolean hasCompleteFutureData(List<WindData> windData) {
        if (windData == null || windData.isEmpty()) {
            return false;
        }

        LocalDate dayAfterTomorrow = LocalDate.now().plusDays(2);
        String targetPattern = dayAfterTomorrow.getDayOfMonth() + "日";

        return windData.stream()
                .anyMatch(data -> data.getTime() != null && data.getTime().contains(targetPattern));
    }

    /**
     * 处理风速数据更新失败的降级策略
     */
    private void handleWindSpeedUpdateFailure(String locationPath, String cacheKey) {
        try {
            // 创建轻量级空元数据，避免重复请求
            WindCacheMetadata emptyMetadata = WindCacheMetadata.builder()
                    .lastUpdated(LocalDateTime.now())
                    .dataSource("fallback")
                    .version("empty")
                    .locationPath(locationPath)
                    .dataCount(0)
                    .build();

            redisUtil.setString(cacheKey, JSON.toJSONString(emptyMetadata), 1800L); // 30分钟过期
            log.info("为{}地区创建空风速数据元数据缓存，避免重复请求", locationPath);
        } catch (Exception fallbackException) {
            log.error("执行风速数据降级策略失败: {}", fallbackException.getMessage());
        }
    }

    @Override
    public void processQueuedRequests() {
        String queueKey = RedisContent.WEATHER_WIND_REQUEST_QUEUE;
        String scheduleLimitKey = RedisContent.getWindKeyWithDate(RedisContent.WEATHER_WIND_SCHEDULE_LIMIT_PREFIX,
                                                                  "global");

        try {
            // 获取队列中的请求
            List<String> queuedRequests = redisUtil.getList(queueKey);

            if (queuedRequests.isEmpty()) {
                log.debug("风速数据请求队列为空");
                return;
            }

            log.info("开始处理队列中的{}个风速数据请求", queuedRequests.size());

            // 使用Set来跟踪本次处理中已经处理过的地区，避免重复处理
            Set<String> processedLocations = new HashSet<>();

            for (String requestJson : queuedRequests) {
                try {
                    // 解析请求信息
                    Map<String, Object> requestInfo = JSON.parseObject(requestJson, Map.class);
                    String locationPath = (String) requestInfo.get("locationPath");
                    Integer retryCount = (Integer) requestInfo.getOrDefault("retryCount", 0);

                    // 检查本次处理中是否已经处理过该地区
                    if (processedLocations.contains(locationPath)) {
                        log.info("{}地区在本次处理中已被处理，从队列中移除重复请求", locationPath);
                        redisUtil.listRemove(queueKey, requestJson);
                        continue;
                    }

                    // 检查调度限流
                    RedisUtil.RequestDelay scheduleRequestDelay = redisUtil.allowRequestWithDelay(
                            scheduleLimitKey, windScheduleLimit, 600, TimeUnit.SECONDS, 1, 300);

                    if (!scheduleRequestDelay.isAllowed()) {
                        log.info("调度限流仍未解除，暂停处理队列请求");
                        break; // 暂停处理，等待下次调度
                    }

                    // 检查缓存是否已存在（可能已被其他请求处理）
                    WindCacheData existingData = getWindCacheData(locationPath);
                    if (existingData != null && existingData.hasValidData() && !shouldUpdateCache(existingData)) {
                        log.info("{}地区的风速数据已存在且有效，从队列中移除", locationPath);
                        redisUtil.listRemove(queueKey, requestJson);
                        continue;
                    }

                    // 标记该地区正在处理
                    processedLocations.add(locationPath);

                    // 如果需要延迟，等待指定时间
                    if (scheduleRequestDelay.getDelay() > 0) {
                        log.info("队列处理请求将在{}秒后执行", scheduleRequestDelay.getDelay());
                        try {
                            Thread.sleep(scheduleRequestDelay.getDelay() * 1000);
                            log.info("队列处理延迟等待完成，开始处理{}地区请求", locationPath);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            log.warn("队列处理{}地区请求延迟被中断，但继续执行: {}", locationPath, e.getMessage());
                        }
                    }

                    // 处理请求
                    boolean success = processQueuedRequest(locationPath);

                    if (success) {
                        // 处理成功，从队列中移除
                        redisUtil.listRemove(queueKey, requestJson);
                        log.info("成功处理队列中{}地区的风速数据请求", locationPath);
                    } else {
                        // 处理失败，增加重试次数
                        retryCount++;
                        if (retryCount >= 3) {
                            // 超过最大重试次数，从队列中移除
                            redisUtil.listRemove(queueKey, requestJson);
                            log.warn("{}地区的风速数据请求重试次数已达上限，从队列中移除", locationPath);
                        } else {
                            // 更新重试次数
                            requestInfo.put("retryCount", retryCount);
                            String updatedRequestJson = JSON.toJSONString(requestInfo);
                            redisUtil.listRemove(queueKey, requestJson);
                            redisUtil.listRightPush(queueKey, updatedRequestJson, 24L, TimeUnit.HOURS);
                            log.info("{}地区的风速数据请求处理失败，重试次数: {}", locationPath, retryCount);
                        }
                    }

                } catch (Exception e) {
                    log.error("处理队列请求时发生异常: {}", e.getMessage(), e);
                }
            }

        } catch (Exception e) {
            log.error("处理风速数据请求队列时发生异常: {}", e.getMessage(), e);
        }
    }

    @Override
    public void addToRequestQueue(String locationPath) {
        try {
            String queueKey = RedisContent.WEATHER_WIND_REQUEST_QUEUE;

            // 1. 检查队列中是否已存在该地区的请求
            if (isLocationAlreadyInQueue(locationPath, queueKey)) {
                log.info("{}地区的风速数据请求已在队列中，跳过重复入队", locationPath);
                return;
            }

            // 2. 创建请求信息对象
            Map<String, Object> requestInfo = new HashMap<>();
            requestInfo.put("locationPath", locationPath);
            requestInfo.put("timestamp", System.currentTimeMillis());
            requestInfo.put("retryCount", 0);

            // 3. 将请求信息序列化并加入队列
            String requestJson = JSON.toJSONString(requestInfo);
            redisUtil.listRightPush(queueKey, requestJson, 24L, TimeUnit.HOURS);

            log.info("已将{}地区的风速数据请求加入队列", locationPath);
        } catch (Exception e) {
            log.error("将请求加入队列失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void cleanupExpiredLocks() {
        try {
            // 这里可以添加清理逻辑，但由于我们设置了锁的过期时间（5分钟），
            // Redis会自动清理过期的锁，所以这个方法主要用于日志记录和监控
            log.debug("执行城市锁清理检查");
        } catch (Exception e) {
            log.error("清理过期锁时发生异常: {}", e.getMessage(), e);
        }
    }

    @Override
    public void forceUnlockCity(String locationPath) {
        try {
            String cityLockKey = RedisContent.WEATHER_WIND_LOCK_PREFIX + locationPath;
            redisUtil.delKey(cityLockKey);
            log.info("已强制释放{}地区的锁", locationPath);
        } catch (Exception e) {
            log.error("强制释放{}地区锁失败: {}", locationPath, e.getMessage(), e);
        }
    }

    /**
     * 处理单个队列请求
     *
     * @param locationPath 地区ID
     * @return 是否处理成功
     */
    private boolean processQueuedRequest(String locationPath) {
        String cacheKey = RedisContent.getWindKey(RedisContent.WEATHER_WIND_HOURLY_PREFIX, locationPath);
        String cityLockKey = RedisContent.WEATHER_WIND_LOCK_PREFIX + locationPath;

        Boolean lockAcquired = false;
        try {
            // 尝试获取城市锁
            lockAcquired = redisUtil.setIfNotExists(cityLockKey, "1", 5L, TimeUnit.MINUTES);
            if (!lockAcquired) {
                log.info("城市{}的数据正在被其他线程处理，跳过队列处理", locationPath);
                return true; // 返回true，因为有其他线程在处理
            }

            // 获取气象局数据（利用两层缓存优化）
            String url = nmcUtil.buildWeatherUrl(locationPath); // 第一层缓存：locationPath -> URL
            List<WindData> newData = weatherHtmlParser.getHourlyWindSpeed(url); // 第二层缓存：URL -> WindData

            if (newData.isEmpty()) {
                log.warn("从气象局获取的风速数据为空，locationPath: {}", locationPath);
                return false;
            }

            // 创建轻量级元数据缓存（不包含实际风速数据，避免重复缓存）
            WindCacheMetadata metadata = WindCacheMetadata.builder()
                    .lastUpdated(LocalDateTime.now())
                    .dataSource("nmc")
                    .version("2.0")
                    .locationPath(locationPath)
                    .dataCount(newData.size())
                    .build();

            // 缓存轻量级元数据，缓存时间6小时（实际数据由@MyCache管理）
            redisUtil.setString(cacheKey, JSON.toJSONString(metadata), TimeUnit.HOURS.toSeconds(6));
            log.info("队列处理：成功更新{}地区风速数据", locationPath);
            return true;

        } catch (Exception e) {
            log.error("队列处理{}地区风速数据失败: {}", locationPath, e.getMessage(), e);
            // 执行降级策略
            handleWindSpeedUpdateFailure(locationPath, cacheKey);
            return false;
        } finally {
            // 释放城市锁
            if (lockAcquired) {
                redisUtil.delKey(cityLockKey);
                log.debug("释放城市{}的处理锁（队列处理）", locationPath);
            }
        }
    }

    /**
     * 检查指定地区是否已在队列中
     *
     * @param locationPath 地区ID
     * @param queueKey     队列键名
     * @return 是否已存在
     */
    private boolean isLocationAlreadyInQueue(String locationPath, String queueKey) {
        try {
            List<String> queuedRequests = redisUtil.getList(queueKey);

            for (String requestJson : queuedRequests) {
                try {
                    Map<String, Object> requestInfo = JSON.parseObject(requestJson, Map.class);
                    String queuedLocationPath = (String) requestInfo.get("locationPath");

                    if (locationPath.equals(queuedLocationPath)) {
                        return true;
                    }
                } catch (Exception e) {
                    log.warn("解析队列请求信息失败: {}", e.getMessage());
                    // 继续检查其他请求
                }
            }

            return false;
        } catch (Exception e) {
            log.error("检查队列中是否存在地区请求失败: {}", e.getMessage(), e);
            // 发生异常时，为了安全起见，允许加入队列
            return false;
        }
    }
}
