package com.jidou.cariad.content.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jidou.cariad.content.base.weather.dao.WeatherAlarmRegionDO;
import com.jidou.cariad.content.base.weather.dao.WeatherNmcCityDO;
import com.jidou.cariad.content.base.weather.dao.entity.WeatherAlarmDO;
import com.jidou.cariad.content.base.weather.dao.entity.WeatherNmcProvinceDO;
import com.jidou.cariad.content.base.weather.service.api.IWeatherAlarmDOService;
import com.jidou.cariad.content.base.weather.service.api.IWeatherNmcProvinceDOService;
import com.jidou.cariad.content.common.content.RedisContent;
import com.jidou.cariad.content.common.converter.WeatherAlarmConverter;
import com.jidou.cariad.content.common.cp.AmapUtil;
import com.jidou.cariad.content.common.cp.NmcUtil;
import com.jidou.cariad.content.common.cp.SeniverseUtil;
import com.jidou.cariad.content.common.enums.BusinessCode;
import com.jidou.cariad.content.common.enums.ElasticSearchIndex;
import com.jidou.cariad.content.common.enums.ProjectVersion;
import com.jidou.cariad.content.common.es.ElasticsearchUtil;
import com.jidou.cariad.content.common.exception.exceptions.BizException;
import com.jidou.cariad.content.common.utils.RedisUtil;
import com.jidou.cariad.content.model.dto.SeniverseWeatherAlarm;
import com.jidou.cariad.content.model.dto.SeniverseWeatherAlarmBase;
import com.jidou.cariad.content.model.vo.baike.CityBaikeVO;
import com.jidou.cariad.content.model.vo.weather.*;
import com.jidou.cariad.content.service.WeatherService;
import com.jidou.cariad.content.wind.cache.WindCacheService;
import com.jidou.cariad.content.wind.dto.WindData;
import com.jidou.cariad.content.wind.util.WindUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.action.search.SearchResponse;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.index.query.BoolQueryBuilder;
import org.opensearch.index.query.QueryBuilders;
import org.opensearch.search.SearchHit;
import org.opensearch.search.SearchHits;
import org.opensearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/16 11:42
 */

@Slf4j
@Service
public class WeatherServiceImpl implements WeatherService {
    // 静态编译正则表达式，提高性能
    private static final java.util.regex.Pattern TIME_PATTERN = java.util.regex.Pattern.compile(
            "(?:(\\d{1,2})日)?(\\d{2}:\\d{2})");
    // 定义时间匹配窗口（秒）
    private static final long TIME_MATCH_WINDOW_SECONDS = 5400;
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private SeniverseUtil seniverseUtil;
    @Resource
    private AmapUtil amapUtil;
    @Resource
    private RestHighLevelClient client;
    @Resource
    private IWeatherAlarmDOService weatherAlarmDOService;
    @Resource
    private WeatherAlarmConverter weatherAlarmConverter;
    @Resource
    private NmcUtil nmcUtil;
    @Resource
    private IWeatherNmcProvinceDOService weatherNmcProvinceDOService;
    @Resource
    private ElasticsearchUtil elasticsearchUtil;
    @Resource
    private WindCacheService windCacheService;
    // 气象局风速处理总开关
    @Value("${weather.wind.enabled:true}")
    private boolean windEnabled;

    private static LocationDTO setSanYaLocationId(LocationDTO locationDTO) {
        locationDTO.setId("W7JZGDR3YYTF");
        locationDTO.setAtSea(true);
        return locationDTO;
    }

    /**
     * 处理风速缓存数据
     *
     * @param hourly       hourly
     * @param cachedData   缓存数据
     * @param locationPath 地区id
     */
    private static void processWindCache(List<WeatherHourlyVO> hourly, String cachedData, String locationPath) {
        try {
            List<WindData> windDataList = JSON.parseObject(cachedData, new TypeReference<List<WindData>>() {
            });
            if (windDataList == null || windDataList.isEmpty()) {
                log.warn("解析到的风力数据列表为空，locationPath: {}", locationPath);
                return;
            }

            // 获取基准日期
            LocalDate baseDate = getBaseDate(hourly);
            log.debug("风力数据处理使用基准日期: {}", baseDate);

            // 使用TreeMap按时间排序，便于找到最接近的时间点
            TreeMap<LocalDateTime, WindData> timeToWindData = buildTimeWindDataMap(windDataList, baseDate);
            if (timeToWindData.isEmpty()) {
                log.warn("无法从风力数据中解析出有效时间格式，locationPath: {}", locationPath);
                return;
            }

            // 填充风力数据
            int matchCount = fillWindData(hourly, timeToWindData);
            log.debug("已从缓存更新{}地区的风力数据，成功匹配{}条数据", locationPath, matchCount);
            // 数据转化 风速转为 km/h，并映射风级
            convertWindSpeedAndSetWindScale(hourly);

        } catch (Exception e) {
            log.error("解析缓存的风力数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取基准日期
     *
     * @param hourly 小时天气数据
     * @return 基准日期
     */
    private static LocalDate getBaseDate(List<WeatherHourlyVO> hourly) {
        LocalDate baseDate = LocalDate.now();
        // 支持跨天，先尝试从hourly中获取最早的日期作为基准
        if (hourly != null && !hourly.isEmpty()) {
            for (WeatherHourlyVO hourlyWeather : hourly) {
                if (hourlyWeather.getTime() != null) {
                    baseDate = hourlyWeather.getTime().toLocalDate();
                    break;
                }
            }
        }
        return baseDate;
    }

    /**
     * 构建时间-风力数据映射
     *
     * @param windDataList 风力数据列表
     * @param baseDate     基准日期
     * @return 时间-风力数据映射
     */
    private static TreeMap<LocalDateTime, WindData> buildTimeWindDataMap(List<WindData> windDataList,
                                                                         LocalDate baseDate) {
        TreeMap<LocalDateTime, WindData> timeToWindData = new TreeMap<>();
        LocalDate currentDate = baseDate;
        LocalDate lastExplicitDate = null;

        for (WindData data : windDataList) {
            if (data.getTime() == null) {
                continue;
            }

            try {
                java.util.regex.Matcher matcher = TIME_PATTERN.matcher(data.getTime());
                if (matcher.matches()) {
                    String dayStr = matcher.group(1);
                    String hmStr = matcher.group(2);

                    // 处理带有明确日期的情况
                    if (dayStr != null) {
                        int day = Integer.parseInt(dayStr);
                        // 更新当前日期
                        currentDate = handleDateTransition(baseDate, day);
                        lastExplicitDate = currentDate;
                    } else if (lastExplicitDate != null) {
                        // 使用上一个明确的日期
                        currentDate = lastExplicitDate;
                    }

                    // 解析时间并创建完整的LocalDateTime
                    java.time.LocalTime localTime = java.time.LocalTime.parse(hmStr);
                    LocalDateTime ldt = LocalDateTime.of(currentDate, localTime);
                    timeToWindData.put(ldt, data);
                } else {
                    log.debug("无法匹配风力数据时间格式: {}", data.getTime());
                }
            } catch (Exception e) {
                log.debug("解析风力数据时间出错: {}, 异常: {}", data.getTime(), e.getMessage());
            }
        }
        return timeToWindData;
    }

    /**
     * 处理日期转换（处理跨月/跨年情况）
     *
     * @param baseDate 基准日期
     * @param day      目标日
     * @return 调整后的日期
     */
    private static LocalDate handleDateTransition(LocalDate baseDate, int day) {
        LocalDate result = baseDate;
        int currentDay = baseDate.getDayOfMonth();
        int currentMonth = baseDate.getMonthValue();

        // 如果目标日小于当前日，可能是下个月
        if (day < currentDay) {
            // 处理月末边界情况
            try {
                result = baseDate.plusMonths(1).withDayOfMonth(day);
            } catch (Exception e) {
                // 处理无效日期（如尝试将4月31日设为6月31日）
                result = baseDate.plusMonths(1).withDayOfMonth(1).plusDays(day - 1);
            }
        } else {
            // 同月内日期调整
            try {
                result = baseDate.withDayOfMonth(day);
            } catch (Exception e) {
                // 处理无效日期
                log.warn("无法设置日期: {}月{}日, 使用默认日期", currentMonth, day);
            }
        }
        return result;
    }

    /**
     * 填充风力数据到hourly列表
     *
     * @param hourly         小时天气数据
     * @param timeToWindData 时间-风力数据映射
     * @return 成功匹配的数据条数
     */
    private static int fillWindData(List<WeatherHourlyVO> hourly, TreeMap<LocalDateTime, WindData> timeToWindData) {
        int matchCount = 0;
        for (WeatherHourlyVO hourlyWeather : hourly) {
            if (hourlyWeather.getTime() == null) {
                continue;
            }

            try {
                // 转换hourly时间为LocalDateTime
                LocalDateTime hourlyTime = hourlyWeather.getTime();

                // 1. 尝试精确匹配
                WindData windData = timeToWindData.get(hourlyTime);

                // 2. 如果精确匹配失败，尝试查找时间窗口内最接近的数据
                if (windData == null) {
                    // 查找小于等于目标时间的最大时间点
                    Map.Entry<LocalDateTime, WindData> floorEntry = timeToWindData.floorEntry(hourlyTime);
                    // 查找大于目标时间的最小时间点
                    Map.Entry<LocalDateTime, WindData> ceilingEntry = timeToWindData.ceilingEntry(hourlyTime);

                    // 在时间窗口内找最接近的
                    windData = findClosestWindData(hourlyTime, floorEntry, ceilingEntry);
                }

                if (windData != null) {
                    hourlyWeather.setWindSpeed(windData.getWindSpeed());
                    hourlyWeather.setWindDirection(windData.getWindDirection());
                    matchCount++;
                }
            } catch (Exception e) {
                log.debug("填充风力数据时出错: {}", e.getMessage());
            }
        }
        return matchCount;
    }

    /**
     * 查找最接近目标时间的风力数据
     *
     * @param targetTime   目标时间
     * @param floorEntry   小于等于目标时间的数据
     * @param ceilingEntry 大于目标时间的数据
     * @return 最接近的风力数据，如果都不在时间窗口内则返回null
     */
    private static WindData findClosestWindData(
            LocalDateTime targetTime,
            Map.Entry<LocalDateTime, WindData> floorEntry,
            Map.Entry<LocalDateTime, WindData> ceilingEntry) {

        // 边界情况处理：两个候选都不存在
        if (floorEntry == null && ceilingEntry == null) {
            return null;
        }

        // 优先处理头部和尾部数据：
        // 1. 如果targetTime在所有风力数据之前（没有floor），直接用最早的数据点
        if (floorEntry == null && ceilingEntry != null) {
            return ceilingEntry.getValue();
        }

        // 2. 如果targetTime在所有风力数据之后（没有ceiling），直接用最晚的数据点
        if (ceilingEntry == null && floorEntry != null) {
            return floorEntry.getValue();
        }

        // 计算时间差（秒）
        long floorDiff = Math.abs(java.time.Duration.between(floorEntry.getKey(), targetTime).getSeconds());
        long ceilingDiff = Math.abs(java.time.Duration.between(targetTime, ceilingEntry.getKey()).getSeconds());

        // 中间数据采用就近原则，但仍有窗口限制
        // 3. 如果两个数据点都存在，选择更近的一个
        if (floorDiff <= ceilingDiff) {
            // 3.1 优先选floor，但要求在窗口内，否则尝试ceiling
            if (floorDiff <= TIME_MATCH_WINDOW_SECONDS) {
                return floorEntry.getValue();
            } else if (ceilingDiff <= TIME_MATCH_WINDOW_SECONDS) {
                // 如果floor太远但ceiling在窗口内，用ceiling
                return ceilingEntry.getValue();
            } else {
                // 3.2 两个都超出窗口，仍用最近的
                log.debug("时间点{}的最近风力数据距离超过设定窗口{}秒，使用最近的风力数据",
                          targetTime, TIME_MATCH_WINDOW_SECONDS);
                return floorEntry.getValue();
            }
        } else {
            // 3.3 优先选ceiling，但要求在窗口内，否则尝试floor
            if (ceilingDiff <= TIME_MATCH_WINDOW_SECONDS) {
                return ceilingEntry.getValue();
            } else if (floorDiff <= TIME_MATCH_WINDOW_SECONDS) {
                // 如果ceiling太远但floor在窗口内，用floor
                return floorEntry.getValue();
            } else {
                // 3.4 两个都超出窗口，仍用最近的
                log.debug("时间点{}的最近风力数据距离超过设定窗口{}秒，使用最近的风力数据",
                          targetTime, TIME_MATCH_WINDOW_SECONDS);
                return ceilingEntry.getValue();
            }
        }
    }

    /**
     * 转换风速单位(m/s -> km/h)并设置风力等级
     *
     * @param hourlyList 小时天气数据列表
     */
    public static void convertWindSpeedAndSetWindScale(List<WeatherHourlyVO> hourlyList) {
        if (hourlyList == null || hourlyList.isEmpty()) {
            return;
        }

        for (WeatherHourlyVO hourly : hourlyList) {
            String windSpeedStr = hourly.getWindSpeed();
            if (windSpeedStr == null || !windSpeedStr.endsWith("m/s")) {
                continue;
            }

            // 使用WindUtils工具类进行转换
            hourly.setWindSpeed(WindUtils.convertWindSpeed(windSpeedStr));
            hourly.setWindScale(WindUtils.mapWindSpeedToScale(windSpeedStr));
        }
    }

    /**
     * 处理风速缓存数据（重载方法，支持WindData列表）
     *
     * @param hourly       小时天气数据
     * @param windDataList 风速数据列表
     * @param locationPath 地区路径
     */
    public static void processWindCache(List<WeatherHourlyVO> hourly, List<WindData> windDataList,
                                        String locationPath) {
        try {
            if (windDataList == null || windDataList.isEmpty()) {
                log.warn("风力数据列表为空，locationPath: {}", locationPath);
                return;
            }

            // 获取基准日期g
            LocalDate baseDate = getBaseDate(hourly);
            log.debug("风力数据处理使用基准日期: {}", baseDate);

            // 使用TreeMap按时间排序，便于找到最接近的时间点
            TreeMap<LocalDateTime, WindData> timeToWindData = buildTimeWindDataMap(windDataList, baseDate);
            if (timeToWindData.isEmpty()) {
                log.warn("无法从风力数据中解析出有效时间格式，locationPath: {}", locationPath);
                return;
            }

            // 填充风力数据
            int matchCount = fillWindData(hourly, timeToWindData);
            log.debug("已从缓存更新{}地区的风力数据，成功匹配{}条数据", locationPath, matchCount);

            // 数据转化 风速转为 km/h，并映射风级
            convertWindSpeedAndSetWindScale(hourly);

        } catch (Exception e) {
            log.error("解析缓存的风力数据失败: {}", e.getMessage(), e);
        }
    }

    private static void stuffExpires(List<WeatherAlarmDO> alarms) {
        // 填充空 expires 为 +1 天
        alarms.forEach(alarm -> {
            if (alarm.getExpires() == null) {
                alarm.setExpires(alarm.getPubDate().plusDays(1));
            }
        });
    }

    /**
     * 获取天气信息
     *
     * @param location 地址
     * @return WeatherInfo
     */
    @Override
    public WeatherInfo getWeather(String location, ProjectVersion projectVersion) {
        WeatherInfo result = new WeatherInfo();
        WeatherNowVO nowWeather;
        String locationName = location;
        try {
            String string = redisUtil.getString(RedisContent.WEATHER_NOW + ":" + location);
            if (StringUtils.isNotBlank(string)) {
                nowWeather = JSON.parseObject(string, WeatherNowVO.class);
            } else {
                LocationDTO locationDto = getLocation2Id(location);
                location = locationDto.getId();
                nowWeather = seniverseUtil.getNowWeather(location);
            }
        } catch (BizException e) {
            if (e.getCode() == BusinessCode.WEATHER_ADDRESS_HAVE_NO_ACCESS_ERROR.getCode()) {
                throw e;
            }
            // 地址没有标准格式化，需要先获取经纬度
            // 获取经纬度
            location = getLocation2LonAndLat(location);
            nowWeather = seniverseUtil.getNowWeather(location);
        }
        result.setNow(nowWeather);

        // 获取逐小时天气
        List<WeatherHourlyVO> hourlyWeather = seniverseUtil.getHourlyWeather(location, 24);
        result.setHourly(hourlyWeather);

        // 获取历史逐小时天气
        List<WeatherHistoryVO> historyWeatherInfo = seniverseUtil.getHourlyHistoryWeatherInfo(location, 24);
        result.setHistory(historyWeatherInfo);

        // 获取逐日天气
        List<WeatherDailyVO> dailyWeather = seniverseUtil.getDailyWeather(location, -1, 15);
        result.setDaily(dailyWeather);

        // 设置逐小时风力等级
        setHourlyWindSpeed(result.getHourly(), projectVersion);
        // 获取预警天气
        List<WeatherAlarmVO> alarmWeatherInfo = getAlarmByProjectVersion(location, locationName, projectVersion);

        // 过滤预警天气的换行字符错误
        alarmWeatherInfo.forEach(alarmWeather -> {
            alarmWeather.setTitle(alarmWeather.getTitle().replace("\n", ""));
            alarmWeather.setDescription(alarmWeather.getDescription().replace("\n", ""));
        });
        result.setAlarm(alarmWeatherInfo);

        return result;
    }

    /**
     * 根据 项目版本获取预警天气
     *
     * @param location 地址
     * @param locationName 原始中文地址
     * @param projectVersion 项目版本
     */
    private List<WeatherAlarmVO> getAlarmByProjectVersion(String location, String locationName,
                                                          ProjectVersion projectVersion) {
        if (projectVersion == ProjectVersion.V2) {
            List<WeatherAlarmDO> alarmDOList = searchAlarm(locationName);
            return weatherAlarmConverter.convertToVOList(alarmDOList);
        } else {
            return seniverseUtil.getAlarmWeatherInfo(location);
        }
    }

    /**
     * 获取location的id
     *
     * @param location 地址
     * @return id
     */
    private LocationDTO getLocation2Id(String location) {
        String string = redisUtil.getString(RedisContent.WEATHER_LOCATIONID + ":" + location);
        if ("noId".equals(string)) {
            return new LocationDTO(location);
        }
        List<LocationDTO> locationDTOS = seniverseUtil.getCityId(location);
        if (locationDTOS != null && !locationDTOS.isEmpty()) {
            return locationDTOS.get(0);
        } else {
            redisUtil.setString(RedisContent.WEATHER_LOCATIONID + ":" + location, "noId");
        }
        return new LocationDTO(location);
    }

    /**
     * 获取对部分locationId进项重定向
     *
     * @param locationDTO 城市DTO
     * @return java.lang.String
     * <AUTHOR>
     * @since 2024/11/27 11:19
     */
    private LocationDTO redirectLocationId(LocationDTO locationDTO) {
        String locationDTOId = locationDTO.getId();
        if (locationDTOId.contains("三沙")
                || locationDTOId.contains("沙群岛")
        ) {
            return setSanYaLocationId(locationDTO);
        }
        switch (locationDTOId) {
            case "W6ZZU0E5REV5": // 三沙市
            case "W6ZZU0QTK2SN":
            case "W982Y5WFG81G":
            case "WDC336NBG99F":
                return setSanYaLocationId(locationDTO);
            default:
                return locationDTO;
        }
    }

    /**
     * 设置逐小时风力等级
     *
     * @param hourly 逐小时天气
     */
    private void setHourlyWindSpeed(List<WeatherHourlyVO> hourly, ProjectVersion projectVersion) {
        if (windEnabled && projectVersion == ProjectVersion.V2) {
            // 委托给专门的风速缓存服务处理
            windCacheService.setHourlyWindSpeed(hourly);
        } else {
            hourly.forEach(hourlyWeather -> hourlyWeather.setWindScale(
                    WindUtils.mapWindSpeedToScale(hourlyWeather.getWindSpeed())));
        }
    }

    /**
     * @param location 地址
     * @return {@link Map}
     */
    @Override
    public Map<String, Object> getAir(String location) {
        HashMap<String, Object> result = new HashMap<>();
        AirNow nowAir;
        LocationDTO locationDTO = null;
        try {
            // 对部分原理大陆的城市进行重定向（查不到空气质量数据）
            locationDTO = redirectLocationId(getLocation2Id(location));
            nowAir = seniverseUtil.getNowAirInfo(locationDTO.getId());
        } catch (BizException e) {
            // 地址没有标准格式化，需要先获取经纬度
            // 获取经纬度
            locationDTO = new LocationDTO(getLocation2LonAndLat(location));
            nowAir = seniverseUtil.getNowAirInfo(locationDTO.getId());
        }
        result.put("now", nowAir);
        // 获取50空气质量排行
        List<AirRanking> airRanking = seniverseUtil.getAirRanking(50);
        result.put("ranking", airRanking);

        // 获取逐小时空气质量
        List<AirHourly> hourlyAirInfo = seniverseUtil.getHourlyAirInfo(locationDTO.getId(), 24);
        result.put("hourly", hourlyAirInfo);

        // 获取逐日空气质量
        List<AirDaily> dailyAirInfo = seniverseUtil.getDailyAirInfo(locationDTO.getId(), 15);
        result.put("daily", dailyAirInfo);

        if (!ObjectUtils.isNull(locationDTO) && locationDTO.isAtSea()) {
            // 对远离大陆的城市进行原始地址填充
            LocationDTO locationOrigin = mapTheSeaLocation(location);
            nowAir.setLocation(locationOrigin);
            hourlyAirInfo.forEach(airHourly -> airHourly.setLocation(locationOrigin));
            dailyAirInfo.forEach(airDaily -> airDaily.setLocation(locationOrigin));
        }
        return result;
    }

    private LocationDTO mapTheSeaLocation(String location) {
        if (location.contains("三沙")) {
            // 三沙市
            return new LocationDTO("W6ZZU0E5REV5", "三沙", "CN", "三沙,三沙,海南,中国", "Asia/Shanghai", "+08:00");
        } else if (location.contains("西沙")) {
            // 西沙群岛
            return new LocationDTO("W6ZZU0QTK2SN", "西沙", "CN", "西沙,西沙,海南,中国", "Asia/Shanghai", "+08:00");
        } else if (location.contains("南沙")) {
            // 南沙群岛
            return new LocationDTO("W982Y5WFG81G", "南沙", "CN", "南沙,南沙,海南,中国", "Asia/Shanghai", "+08:00");
        } else if (location.contains("中沙")) {
            // 中沙群岛的岛礁及其海域
            return new LocationDTO("WDC336NBG99F", "中沙", "CN", "中沙,中沙,海南,中国", "Asia/Shanghai", "+08:00");
        }
        return new LocationDTO(location);
    }

    @Override
    public Map<String, Object> getLife(String location, Integer dayNum) {

        HashMap<String, Object> result = new HashMap<>();
        List<Object> life;

        try {
            LocationDTO locationDTO = getLocation2Id(location);
            location = locationDTO.getId();
            life = seniverseUtil.getLifeSuggestion(location, dayNum);
        } catch (BizException e) {
            // 地址没有标准格式化，需要先获取经纬度
            // 获取经纬度
            location = getLocation2LonAndLat(location);
            life = seniverseUtil.getLifeSuggestion(location, dayNum);
        }
        result.put("life", life);
        return result;
    }

    @Override
    public SunAndMoonInfo getSunAndMoon(String location, String date) {

        SunAndMoonInfo result = new SunAndMoonInfo();
        List<Sun> sunList;
        try {
            LocationDTO locationDTO = getLocation2Id(location);
            location = locationDTO.getId();
            sunList = seniverseUtil.getSunriseSunset(location, date, 15);
        } catch (BizException e) {
            // 地址没有标准格式化，需要先获取经纬度
            // 获取经纬度
            location = getLocation2LonAndLat(location);
            sunList = seniverseUtil.getSunriseSunset(location, date, 15);
        }
        List<Moon> moonList = seniverseUtil.getMoon(location, date, 15);
        for (int i = 0; i < 15; i++) {
            SunAndMoon sunAndMoon = new SunAndMoon();
            sunAndMoon.setSun(sunList.get(i));
            sunAndMoon.setMoon(moonList.get(i));
        }
        SunAndMoon sunAndMoon = new SunAndMoon();
        sunAndMoon.setSun(sunList.get(0));
        sunAndMoon.setMoon(moonList.get(0));
        result.setSunAndMoon(sunAndMoon);
        return result;
    }

    @Override
    public Map<String, Object> getAll(String location, String date,  ProjectVersion projectVersion) {
        HashMap<String, Object> result = new HashMap<>();
        WeatherInfo weather = getWeather(location,  projectVersion);
        Map<String, Object> air = getAir(location);
        Map<String, Object> life = getLife(location, 5);
        SunAndMoonInfo sunAndMoon = getSunAndMoon(location, date);
        result.put("weather", weather);
        result.put("air", air);
        result.put("life", life);
        result.put("sunAndMoon", sunAndMoon);
        return result;
    }

    @Override
    public CityBaikeVO getBaikeTodayWeatherInfo(String city) {
        WeatherInfo weather = getWeather(city,  ProjectVersion.V1);
        List<WeatherDailyVO> daily = weather.getDaily();
        if (daily == null) {
            return null;
        }
        CityBaikeVO cityBaikeVO = new CityBaikeVO();
        Map<String, Object> air = getAir(city);

        LocalDate now = LocalDate.now();
        // 获取当日的天气数据
        daily.forEach(weatherDailyVO -> {
            if (now.isEqual(weatherDailyVO.getDate().toLocalDate())) {
                cityBaikeVO.putDaily("weatherInfo", weatherDailyVO);
            }
        });
        // 获取当日的空气数据
        List<AirDaily> airDailyList = (List<AirDaily>) air.get("daily");
        airDailyList.forEach(airDaily -> {
            if (now.isEqual(airDaily.getDate().toLocalDate())) {
                cityBaikeVO.putDaily("airInfo", airDaily);
            }
        });
        return cityBaikeVO;
    }

    /**
     * 获取经纬度
     *
     * @param location 地址
     * @return 经纬度
     */
    private String getLocation2LonAndLat(String location) {

        Map<String, Object> locationM = amapUtil.getLocation(location);
        String lonAndLat = (String) locationM.get("location");
        // 保留经纬度后两位
        String[] lonAndlatArray = lonAndLat.split(",");
        String longitude = lonAndlatArray[0].substring(0, lonAndlatArray[0].indexOf(".") + 3);
        String latitude = lonAndlatArray[1].substring(0, lonAndlatArray[1].indexOf(".") + 3);
        lonAndLat = latitude + ":" + longitude;
        return lonAndLat;
    }

    /**
     * 导入极端天气地区信息
     *
     * @param file csv文件，格式：adcode,城市名
     * @return boolean 是否导入成功
     */
    @Override
    public boolean importAlarmRegion(MultipartFile file) {
        // 读取CSV
        List<WeatherAlarmRegionDO> regionList = readCsv(file);
        regionList.forEach(region -> region.setCreateTime(LocalDateTime.now()));

        // 先清空ES
        elasticsearchUtil.clearIndex(ElasticSearchIndex.WEATHER_ALARM_REGION);

        // 导入数据到ES
        elasticsearchUtil.batchSaveToEs(regionList, ElasticSearchIndex.WEATHER_ALARM_REGION);
        return true;
    }

    @Override
    public void rePullAlarm() {
        // 清空数据库
        weatherAlarmDOService.remove(new QueryWrapper<>());

        SeniverseWeatherAlarmBase alarmBase = seniverseUtil.getAlarmList(null);
        List<SeniverseWeatherAlarm> results = alarmBase.getResults();
        if (results == null || results.isEmpty()) {
            return;
        }
        SeniverseWeatherAlarm seniverseWeatherAlarm = results.get(0);
        List<WeatherAlarmDO> alarms = seniverseWeatherAlarm.getAlarms();
        stuffExpires(alarms);
        boolean b = weatherAlarmDOService.saveBatch(alarms);
        log.info("极端天气保存数据到数据库: {}", b);

        // 获取最后更新时间戳
        Long lastUpdateTs = seniverseWeatherAlarm.getLastUpdateTs();
        redisUtil.setString(RedisContent.WEATHER_ALARM_LAST_UPDATE_TS, lastUpdateTs.toString(), 60 * 30L);
    }

    @Override
    public void syncAlarm() {
        // 先删除已过期的数据
        LambdaQueryWrapper<WeatherAlarmDO> overdueAlarmWrapper = new LambdaQueryWrapper<>();
        overdueAlarmWrapper.lt(WeatherAlarmDO::getExpires, LocalDateTime.now());
        overdueAlarmWrapper.or().isNull(WeatherAlarmDO::getExpires);
        weatherAlarmDOService.remove(overdueAlarmWrapper);
        log.info("已删除已过期的数据");

        // 获取到 lastUpdateTs
        String lastUpdateTs = redisUtil.getString(RedisContent.WEATHER_ALARM_LAST_UPDATE_TS);
        SeniverseWeatherAlarmBase alarmBase = seniverseUtil.getAlarmList(lastUpdateTs);

        List<SeniverseWeatherAlarm> results = alarmBase.getResults();
        if (results == null || results.isEmpty()) {
            return;
        }
        SeniverseWeatherAlarm seniverseWeatherAlarm = results.get(0);
        List<WeatherAlarmDO> alarms = seniverseWeatherAlarm.getAlarms();
        stuffExpires(alarms);
        log.info("本次同步数据条数: {}", alarms.size());
        weatherAlarmDOService.saveOrUpdateBatch(alarms);
        log.info("已保存到数据库");

        //  更新 lastUpdateTs
        Long newlastUpdateTs = seniverseWeatherAlarm.getLastUpdateTs();
        redisUtil.setString(RedisContent.WEATHER_ALARM_LAST_UPDATE_TS, newlastUpdateTs.toString(), 60 * 30L);
    }

    @Override
    public void syncNmc() {
        // 清空数据库
        weatherNmcProvinceDOService.remove(new QueryWrapper<>());
        List<WeatherNmcProvinceDO> provinceList = nmcUtil.getProvinceList();
        if (provinceList != null && !provinceList.isEmpty()) {
            weatherNmcProvinceDOService.saveBatch(provinceList);
        }

        // 清空 ES 中的数据
        elasticsearchUtil.clearIndex(ElasticSearchIndex.WEATHER_NMC_CITY);

        // 遍历省份，获取城市列表并保存到 ES
        List<WeatherNmcCityDO> allCities = new ArrayList<>();
        provinceList.forEach(province -> {
            List<WeatherNmcCityDO> cityList = nmcUtil.getCityList(province.getCode());
            allCities.addAll(cityList);
        });

        // 批量保存到 ES
        elasticsearchUtil.batchSaveToEs(allCities, ElasticSearchIndex.WEATHER_NMC_CITY);
    }

    @Override
    public List<WeatherAlarmDO> searchAlarm(String location) {
        // 通过 ES 查询获取地区信息
        WeatherAlarmRegionDO region = searchAlarmRegionFromEs(location);
        if (region != null) {
            log.info("找到匹配的地区信息: {}", region);
            List<WeatherAlarmDO> alarmDOList = searchAlarmByRegionInDB(region);
            return filterAlarm(alarmDOList);
        } else {
            log.info("未找到匹配的地区信息");
            return Collections.emptyList();
        }
    }

    private List<WeatherAlarmDO> filterAlarm(List<WeatherAlarmDO> alarmDOList) {
        // TODO 过滤掉冗余的极端天气
        if (alarmDOList != null && !alarmDOList.isEmpty()) {
            // 过滤掉过期的预警信息
            alarmDOList = alarmDOList.stream().filter(alarm -> alarm.getExpires().isAfter(LocalDateTime.now())).collect(
                    Collectors.toList());
            // 按照发布时间倒序排序
            alarmDOList = alarmDOList.stream().sorted(
                    Comparator.comparing(WeatherAlarmDO::getPubDate).reversed()).collect(
                    Collectors.toList());
            // 按照预警信号种类进行归类
            Map<String, List<WeatherAlarmDO>> alarmListMap = alarmDOList.stream().collect(
                    Collectors.groupingBy(WeatherAlarmDO::getType));
            // 每种类型里，根据Alert, Cancel, Update保留最新的一个状态，如果最新为 Cancel 则删除
            alarmListMap.forEach((type, list) -> {
                if (list.size() > 0) {
                    if (list.get(0).getStatus().equals("Cancel")) {
                        list.removeAll(list);
                    } else {
                        list.removeIf(alarm -> alarm.getStatus().equals("Cancel"));
                    }
                }
            });
            // 仅保留一个最新的
            return alarmListMap.values().stream()
                    .filter(list -> !list.isEmpty())
                    .map(list -> list.get(0))
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private List<WeatherAlarmDO> searchAlarmByRegionInDB(WeatherAlarmRegionDO region) {
        if (ObjectUtils.isEmpty(region)) {
            return Collections.emptyList();
        }
        String adcode = region.getAdcode();
        // TODO 查询数据库中的极端天气信息
        LambdaQueryWrapper<WeatherAlarmDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WeatherAlarmDO::getRegionId, adcode);
        List<WeatherAlarmDO> alarmDOList = weatherAlarmDOService.list(wrapper);
        return alarmDOList;
    }

    private WeatherAlarmRegionDO searchAlarmRegionFromEs(String location) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.matchQuery("cityName", location));
        try {
            SearchRequest searchRequest = new SearchRequest(ElasticSearchIndex.WEATHER_ALARM_REGION.getIndexName());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQueryBuilder);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits hits = searchResponse.getHits();
            if (hits.getTotalHits().value > 0) {
                SearchHit hit = hits.getAt(0);
                return JSON.parseObject(hit.getSourceAsString(), WeatherAlarmRegionDO.class);
            }
        } catch (IOException e) {
            log.error("查询地区信息失败: {}1", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 读取CSV文件
     *
     * @param file csv文件
     * @return 地区列表
     */
    private List<WeatherAlarmRegionDO> readCsv(MultipartFile file) {
        try {
            return EasyExcelFactory.read(file.getInputStream()).head(WeatherAlarmRegionDO.class).sheet().doReadSync();
        } catch (IOException e) {
            log.error("读取CSV发生错误: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

}
