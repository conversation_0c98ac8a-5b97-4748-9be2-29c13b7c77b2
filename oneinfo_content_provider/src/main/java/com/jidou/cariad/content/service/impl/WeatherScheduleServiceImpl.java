package com.jidou.cariad.content.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jidou.cariad.content.base.weather.dao.entity.WeatherNmcCityScheduleDO;
import com.jidou.cariad.content.base.weather.service.api.IWeatherNmcCityScheduleDOService;
import com.jidou.cariad.content.common.content.RedisContent;
import com.jidou.cariad.content.common.utils.RedisUtil;
import com.jidou.cariad.content.service.WeatherScheduleService;
import com.jidou.cariad.content.wind.cache.WindCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 天气数据调度服务实现类
 * 专注于调度逻辑管理，直接调用WindCacheService进行数据更新
 *
 * <AUTHOR>
 * @since 2024/12/19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WeatherScheduleServiceImpl implements WeatherScheduleService {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final IWeatherNmcCityScheduleDOService cityScheduleService;
    private final RedisUtil redisUtil;
    private final WindCacheService windCacheService;
    @Value("${weather.schedule.enabled:true}")
    private boolean scheduleEnabled;
    @Value("${weather.schedule.cycle-days:3}")
    private int cycleDays;
    @Value("${weather.schedule.batch-size:3}")
    private int batchSize;
    @Value("${weather.schedule.retry-max-count:3}")
    private int retryMaxCount;

    @Value("${weather.wind.schedule-limit:3}")
    private int windScheduleLimit;

    @Override
    public void initCitySchedules() {
        if (!scheduleEnabled) {
            throw new IllegalStateException("城市调度功能已禁用，无法执行初始化");
        }

        log.info("开始为现有城市记录设置调度时间...");

        try {
            List<WeatherNmcCityScheduleDO> allCities = cityScheduleService.list();

            if (allCities.isEmpty()) {
                throw new IllegalStateException("数据库中没有城市记录，请先通过SQL手动注入城市路径数据");
            }

            int updatedCount = 0;
            for (WeatherNmcCityScheduleDO city : allCities) {
                LocalDateTime nextUpdateTime = generateRandomUpdateTime(city.getPath(), cycleDays);

                LambdaUpdateWrapper<WeatherNmcCityScheduleDO> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(WeatherNmcCityScheduleDO::getPath, city.getPath())
                        .set(WeatherNmcCityScheduleDO::getNextUpdateTime, nextUpdateTime)
                        .set(WeatherNmcCityScheduleDO::getStatus, 0)
                        .set(WeatherNmcCityScheduleDO::getRetryCount, 0)
                        .set(WeatherNmcCityScheduleDO::getUpdateResult, "INITIALIZED");

                if (cityScheduleService.update(updateWrapper)) {
                    updatedCount++;
                    log.debug("为城市{}设置调度时间: {}", city.getPath(), nextUpdateTime);
                }
            }

            log.info("城市调度时间设置完成，共处理{}个城市", updatedCount);

        } catch (Exception e) {
            log.error("设置城市调度时间失败: {}", e.getMessage(), e);
            throw new RuntimeException("初始化失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void scanAndUpdateExpiredCities() {
        if (!scheduleEnabled) {
            log.debug("调度功能已禁用，跳过扫描");
            return;
        }

        String scheduleLimitKey = RedisContent.getWindKeyWithDate(RedisContent.WEATHER_WIND_SCHEDULE_LIMIT_PREFIX,
                                                                  "global");

        try {
            LambdaQueryWrapper<WeatherNmcCityScheduleDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.le(WeatherNmcCityScheduleDO::getNextUpdateTime, LocalDateTime.now())
                    .eq(WeatherNmcCityScheduleDO::getStatus, 0)
                    .lt(WeatherNmcCityScheduleDO::getRetryCount, retryMaxCount)
                    .orderByAsc(WeatherNmcCityScheduleDO::getNextUpdateTime)
                    .last("LIMIT " + (batchSize * 2));

            List<WeatherNmcCityScheduleDO> expiredCities = cityScheduleService.list(queryWrapper);

            if (expiredCities.isEmpty()) {
                log.debug("当前无需要更新的城市");
                return;
            }

            log.info("扫描到{}个需要更新的城市", expiredCities.size());

            int processedCount = 0;
            for (WeatherNmcCityScheduleDO city : expiredCities) {
                if (processedCount >= batchSize) {
                    log.info("已处理{}个城市，达到批次限制，剩余城市将在下次调度处理", processedCount);
                    break;
                }

                RedisUtil.RequestDelay scheduleRequestDelay = redisUtil.allowRequestWithDelay(
                        scheduleLimitKey, windScheduleLimit, 600, TimeUnit.SECONDS, 1, 300);

                if (!scheduleRequestDelay.isAllowed()) {
                    log.info("调度限流达到上限，剩余城市将在下次调度处理");
                    break;
                }

                if (scheduleRequestDelay.getDelay() > 0) {
                    try {
                        Thread.sleep(scheduleRequestDelay.getDelay() * 1000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("调度延迟被中断: {}", e.getMessage());
                        break;
                    }
                }

                try {
                    // 直接调用WindCacheService为计划内城市更新数据，遵循显式优于隐式原则
                    windCacheService.updateWindSpeedDataForScheduledCity(city.getPath());

                    updateCityNextScheduleTime(city.getPath(), cycleDays);

                    log.info("已触发城市{}的数据更新调度", city.getPath());
                    processedCount++;

                } catch (Exception e) {
                    log.error("处理城市{}调度时发生异常: {}", city.getPath(), e.getMessage(), e);
                    incrementRetryCount(city.getPath(), e.getMessage());
                }
            }

            log.info("本次调度完成，共处理{}个城市", processedCount);

        } catch (Exception e) {
            log.error("扫描和更新到期城市时发生异常: {}", e.getMessage(), e);
        }
    }

    @Override
    public void updateCityNextScheduleTime(String cityPath, int cycleDays) {
        LocalDateTime nextUpdateTime = LocalDateTime.now().plusDays(cycleDays);

        LambdaUpdateWrapper<WeatherNmcCityScheduleDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WeatherNmcCityScheduleDO::getPath, cityPath)
                .set(WeatherNmcCityScheduleDO::getNextUpdateTime, nextUpdateTime.format(DATE_TIME_FORMATTER))
                .set(WeatherNmcCityScheduleDO::getLastUpdateTime, LocalDateTime.now().format(DATE_TIME_FORMATTER))
                .set(WeatherNmcCityScheduleDO::getUpdateTime, LocalDateTime.now())
                .set(WeatherNmcCityScheduleDO::getRetryCount, 0)
                .set(WeatherNmcCityScheduleDO::getUpdateResult, "success");

        boolean updated = cityScheduleService.update(updateWrapper);
        if (updated) {
            log.debug("更新城市{}下次调度时间为: {}", cityPath, nextUpdateTime);
        } else {
            log.warn("更新城市{}调度时间失败", cityPath);
        }
    }

    @Override
    public LocalDateTime generateRandomUpdateTime(String cityPath, int cycleDays) {
        long seed = cityPath.hashCode() + LocalDate.now().toEpochDay();
        Random random = new Random(seed);

        int randomDays = random.nextInt(cycleDays);
        int randomHours;
        int randomMinutes;
        // 保证至少是今天，并且至少比现在时间晚15分钟，避免生成今天稍早或过于接近当前时间的调度
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime baseTime;

        // 如果是当天（randomDays == 0），则确保时间至少比现在晚15分钟
        if (randomDays == 0) {
            baseTime = now.plusMinutes(15); // 当天至少延迟15分钟
            // 当天至少延迟15分钟，生成从当前时间+15分钟到24点之间的随机时间
            int remainingMinutes = 24 * 60 - (baseTime.getHour() * 60 + baseTime.getMinute());
            int randomOffset = random.nextInt(remainingMinutes);
            // 计算小时部分：从baseTime的小时开始加上偏移量的小时数
            randomHours = baseTime.getHour() + (baseTime.getMinute() + randomOffset) / 60;
            // 计算分钟部分：偏移量的余数
            randomMinutes = (baseTime.getMinute() + randomOffset) % 60;
        } else {
            // 非当天可自由分配小时和分钟
            randomHours = random.nextInt(24);
            randomMinutes = random.nextInt(60);
        }

        LocalDateTime result = now
                .plusDays(randomDays)
                .withHour(randomHours)
                .withMinute(randomMinutes)
                .withSecond(0)
                .withNano(0);

        log.debug("为城市{}生成调度时间: {} (种子: {})", cityPath, result, seed);
        return result;
    }

    @Override
    public boolean needsInitialization() {
        try {
            LambdaQueryWrapper<WeatherNmcCityScheduleDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.isNull(WeatherNmcCityScheduleDO::getNextUpdateTime)
                    .or()
                    .eq(WeatherNmcCityScheduleDO::getStatus, null);

            Long countWithoutSchedule = cityScheduleService.count(queryWrapper);

            if (countWithoutSchedule > 0) {
                log.info("发现{}个城市记录缺少调度时间设置", countWithoutSchedule);
                return true;
            }

            Long totalCount = cityScheduleService.count();
            if (totalCount == 0) {
                log.info("数据库中没有城市记录");
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("检查初始化状态失败: {}", e.getMessage(), e);
            return true;
        }
    }

    @Override
    public void handleScheduleFailure(String cityPath, String errorMessage) {
        try {
            LambdaQueryWrapper<WeatherNmcCityScheduleDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WeatherNmcCityScheduleDO::getPath, cityPath);

            WeatherNmcCityScheduleDO citySchedule = cityScheduleService.getOne(queryWrapper);
            if (citySchedule == null) {
                log.warn("未找到城市{}的调度记录", cityPath);
                return;
            }

            int newRetryCount = citySchedule.getRetryCount() + 1;
            int newStatus = newRetryCount >= retryMaxCount ? 2 : 0;

            LambdaUpdateWrapper<WeatherNmcCityScheduleDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(WeatherNmcCityScheduleDO::getPath, cityPath)
                    .set(WeatherNmcCityScheduleDO::getRetryCount, newRetryCount)
                    .set(WeatherNmcCityScheduleDO::getStatus, newStatus)
                    .set(WeatherNmcCityScheduleDO::getUpdateResult, errorMessage)
                    .set(WeatherNmcCityScheduleDO::getLastUpdateTime, LocalDateTime.now());

            cityScheduleService.update(updateWrapper);

            if (newStatus == 2) {
                log.error("城市{}重试次数达到上限，标记为异常状态", cityPath);
            } else {
                log.warn("城市{}调度失败，重试次数: {}", cityPath, newRetryCount);
            }

        } catch (Exception e) {
            log.error("处理城市{}调度失败时发生异常: {}", cityPath, e.getMessage(), e);
        }
    }

    @Override
    public void resetCityScheduleStatus(String cityPath) {
        try {
            LambdaUpdateWrapper<WeatherNmcCityScheduleDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(WeatherNmcCityScheduleDO::getPath, cityPath)
                    .set(WeatherNmcCityScheduleDO::getStatus, 0)
                    .set(WeatherNmcCityScheduleDO::getRetryCount, 0)
                    .set(WeatherNmcCityScheduleDO::getUpdateResult, "RESET")
                    .set(WeatherNmcCityScheduleDO::getNextUpdateTime, generateRandomUpdateTime(cityPath, cycleDays));

            cityScheduleService.update(updateWrapper);
            log.info("重置城市{}调度状态成功", cityPath);

        } catch (Exception e) {
            log.error("重置城市{}调度状态失败: {}", cityPath, e.getMessage(), e);
        }
    }

    @Override
    public ScheduleStatistics getScheduleStatistics() {
        try {
            ScheduleStatistics stats = new ScheduleStatistics();

            stats.setTotalCities(cityScheduleService.count());

            LambdaQueryWrapper<WeatherNmcCityScheduleDO> normalQuery = new LambdaQueryWrapper<>();
            normalQuery.eq(WeatherNmcCityScheduleDO::getStatus, 0);
            stats.setNormalCities(cityScheduleService.count(normalQuery));

            LambdaQueryWrapper<WeatherNmcCityScheduleDO> pausedQuery = new LambdaQueryWrapper<>();
            pausedQuery.eq(WeatherNmcCityScheduleDO::getStatus, 1);
            stats.setPausedCities(cityScheduleService.count(pausedQuery));

            LambdaQueryWrapper<WeatherNmcCityScheduleDO> errorQuery = new LambdaQueryWrapper<>();
            errorQuery.eq(WeatherNmcCityScheduleDO::getStatus, 2);
            stats.setErrorCities(cityScheduleService.count(errorQuery));

            LambdaQueryWrapper<WeatherNmcCityScheduleDO> expiredQuery = new LambdaQueryWrapper<>();
            expiredQuery.le(WeatherNmcCityScheduleDO::getNextUpdateTime, LocalDateTime.now())
                    .eq(WeatherNmcCityScheduleDO::getStatus, 0);
            stats.setExpiredCities(cityScheduleService.count(expiredQuery));

            return stats;

        } catch (Exception e) {
            log.error("获取调度统计信息失败: {}", e.getMessage(), e);
            return new ScheduleStatistics();
        }
    }

    @Override
    public Optional<WeatherNmcCityScheduleDO> getCityScheduleInfo(String locationPath) {
        try {
            LambdaQueryWrapper<WeatherNmcCityScheduleDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WeatherNmcCityScheduleDO::getPath, locationPath);

            WeatherNmcCityScheduleDO schedule = cityScheduleService.getOne(queryWrapper);
            return Optional.ofNullable(schedule);
        } catch (Exception e) {
            log.error("查询城市{}调度信息失败: {}", locationPath, e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public void rescheduleImmediately(String locationPath) {
        try {
            LambdaUpdateWrapper<WeatherNmcCityScheduleDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(WeatherNmcCityScheduleDO::getPath, locationPath)
                    .set(WeatherNmcCityScheduleDO::getNextUpdateTime, LocalDateTime.now())
                    .set(WeatherNmcCityScheduleDO::getStatus, 0)
                    .set(WeatherNmcCityScheduleDO::getRetryCount, 0)
                    .set(WeatherNmcCityScheduleDO::getUpdateTime, LocalDateTime.now())
                    .set(WeatherNmcCityScheduleDO::getUpdateResult, "cache-miss-reschedule");

            boolean updated = cityScheduleService.update(updateWrapper);
            if (updated) {
                log.info("成功重置城市{}的调度时间为立即执行", locationPath);
            } else {
                log.warn("重置城市{}调度时间失败", locationPath);
            }

        } catch (Exception e) {
            log.error("重置城市{}调度时间失败: {}", locationPath, e.getMessage(), e);
            throw new RuntimeException("重置调度时间失败", e);
        }
    }

    @Override
    public void addCitySchedule(String path, Integer cycleDays) {
        if (path == null || path.trim().isEmpty()) {
            throw new IllegalArgumentException("城市路径不能为空");
        }

        path = path.trim();

        // 使用传入的周期天数，如果为null则使用默认配置
        int actualCycleDays = cycleDays != null ? cycleDays : this.cycleDays;

        try {
            // 检查城市路径是否已存在
            LambdaQueryWrapper<WeatherNmcCityScheduleDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WeatherNmcCityScheduleDO::getPath, path);

            WeatherNmcCityScheduleDO existingCity = cityScheduleService.getOne(queryWrapper);
            if (existingCity != null) {
                throw new IllegalStateException("城市路径 " + path + " 已存在调度记录");
            }

            // 创建新的调度记录
            WeatherNmcCityScheduleDO newCity = new WeatherNmcCityScheduleDO();
            newCity.setPath(path);
            newCity.setNextUpdateTime(generateRandomUpdateTime(path, actualCycleDays));
            newCity.setStatus(0); // 正常状态
            newCity.setRetryCount(0);
            newCity.setLastUpdateTime(null); // 首次添加，无上次更新时间
            newCity.setUpdateResult("MANUAL_ADDED");
            newCity.setCreateTime(LocalDateTime.now());
            newCity.setUpdateTime(LocalDateTime.now());

            boolean saved = cityScheduleService.save(newCity);
            if (saved) {
                log.info("成功添加城市{}的调度记录，下次更新时间: {}", path, newCity.getNextUpdateTime());
            } else {
                throw new RuntimeException("保存城市调度记录失败");
            }

        } catch (IllegalArgumentException | IllegalStateException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("添加城市{}调度记录时发生异常: {}", path, e.getMessage(), e);
            throw new RuntimeException("添加城市调度记录失败: " + e.getMessage(), e);
        }
    }

    private void incrementRetryCount(String cityPath, String errorMessage) {
        try {
            LambdaUpdateWrapper<WeatherNmcCityScheduleDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(WeatherNmcCityScheduleDO::getPath, cityPath)
                    .setSql("retry_count = retry_count + 1")
                    .set(WeatherNmcCityScheduleDO::getUpdateTime, LocalDateTime.now())
                    .set(WeatherNmcCityScheduleDO::getUpdateResult, "error: " + errorMessage);

            cityScheduleService.update(updateWrapper);
            log.warn("城市{}调度失败，已增加重试计数", cityPath);

        } catch (Exception e) {
            log.error("增加城市{}重试计数失败: {}", cityPath, e.getMessage(), e);
        }
    }
}
