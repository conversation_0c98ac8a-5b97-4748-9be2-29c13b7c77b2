# 气象风速数据处理流程文档

## 概述

本系统从中国气象局网站获取风速数据，提供给下游服务使用。采用**被动响应** + **主动调度**双重策略：

- **被动响应**：用户请求时检查缓存，区分计划内外城市
- **主动调度**：定时维护计划内城市数据新鲜度

## 核心架构流程

### 用户请求处理流程

```mermaid
graph TD
    A[用户/下游服务] -->|API请求| B[WindSpeedController]
    B -->|调用服务| C[WeatherService接口]
    C -->|实现| D[WeatherServiceImpl.getWeather]
    D -->|委托处理| E[WindCacheService.setHourlyWindSpeed]
    E -->|检查缓存| F{风速缓存是否存在且有效?}
    F -->|存在且有效| G[processWindCache处理缓存数据]
    F -->|不存在或需更新| H[CityScheduleCacheService检查城市状态]
    H -->|查询调度信息| I{是否为计划内城市?}
%% 计划内城市处理流程
    I -->|是计划内城市| J[直接处理现有缓存数据]
    J -->|理论上应有有效缓存| K{缓存确实有效?}
    K -->|有效| L[processWindCache处理数据]
    K -->|异常情况| M[二级降级策略]
    M -->|1 . 历史缓存| N[使用历史数据]
    M -->|2 . 默认数据| P[返回心知数据]
%% 计划外城市处理流程
    I -->|非计划内城市| Q[UnscheduledCityAlertManager发送预警]
    Q -->|企微预警通知| R[WeatherAlertService预警]
    Q -->|同步获取数据| S[同步调用updateWindSpeedData]
    S -->|城市锁+响应限流检查| T{响应限流检查}
    T -->|限流通过| U[WeatherHtmlParser解析获取数据]
    T -->|限流失败| V[addToRequestQueue加入队列]
    V -->|立即返回| W[返回默认数据给用户]
    U -->|解析失败| Y[AOP捕获异常+降级处理]
    U -->|解析成功| X[更新Redis缓存+返回数据]
    Y -->|异常情况| M[二级降级策略]
%% 队列处理机制
    V -->|加入Redis队列| Z1[Redis请求队列]
    Z1 -->|定时任务处理| Z2[ProcessWindSpeedQueueJobHandler]
    Z2 -->|批量处理队列| Z3[WindCacheService.processQueuedRequests]
    Z3 -->|调度限流检查通过| Z4[WeatherHtmlParser解析]
    Z4 -->|解析成功| Z5[更新Redis缓存]
    Z4 -->|解析失败| Z6[记录失败+重试机制]
%% 统一响应
    G -->|返回结果| AA[API响应]
    L -->|返回结果| AA
    N -->|返回结果| AA
    P -->|返回结果| AA
    W -->|返回结果| AA
    X -->|返回结果| AA
    Y -->|返回结果| AA
```

### 主动调度流程

```mermaid
graph TD
    A[应用启动] -->|检查数据库| B{cities表是否已初始化?}
    B -->|否| C[initCitySchedules初始化]
    B -->|是| D[系统正常运行]
    C -->|随机分布6天内| E[生成next_update_time]
    E -->|批量写入数据库| F[初始化完成]
    F --> D
    D --> G[每10分钟定时任务]
    G --> H[scanAndUpdateExpiredCities]
    H --> I[查询next_update_time <= now的城市]
    I --> J{是否有到期城市?}
    J -->|否| K[等待下次调度]
    J -->|是| L[检查调度限流]
    L -->|限流通过| M[选择1-3个城市批量更新]
    L -->|限流失败| N[记录失败原因]
    M --> O[调用updateWindSpeedDataForScheduledCity]
    O -->|直接执行无限流| P[updateCityNextScheduleTime +6天]
    O -->|失败| Q[retryCount++, 异常处理]
    P --> R[记录更新结果]
    Q --> S{重试次数 < 3?}
    S -->|是| T[延迟重试]
    S -->|否| U[标记为异常状态]
    K --> G
    N --> G
    R --> G
    T --> G
    U --> G
```

### 系统时序图

```mermaid
sequenceDiagram
    participant 客户端
    participant Controller as WindSpeedController
    participant WeatherService as WeatherServiceImpl
    participant WindCacheService as WindCacheServiceImpl
    participant ScheduleService as WeatherScheduleService
    participant Parser as WeatherHtmlParser
    participant AOP as WeatherAlertAspect
    participant 气象局网站
    participant 企微机器人
    participant Queue as Redis队列
    participant JobHandler as WeatherScheduleJobHandler
    participant Database as 城市调度表
%% 应用启动初始化流程
    Note over ScheduleService, Database: 应用启动初始化
    ScheduleService ->> Database: 检查是否需要初始化
    Database -->> ScheduleService: 返回城市数量
    alt 需要初始化
        ScheduleService ->> ScheduleService: 生成随机分布时间
        ScheduleService ->> Database: 批量插入城市调度时间
    end

%% 主动调度流程
    Note over JobHandler, Database: 定时调度流程（每10分钟）
    JobHandler ->> ScheduleService: 扫描到期城市
    ScheduleService ->> Database: 查询需要更新的城市
    Database -->> ScheduleService: 返回到期城市列表
    loop 批量处理城市
        ScheduleService ->> ScheduleService: 检查调度限流
        alt 限流通过
            ScheduleService ->> WindCacheService: updateWindSpeedDataForScheduledCity(直接执行)
            ScheduleService ->> Database: 更新next_update_time+6天
        else 限流失败
            ScheduleService ->> ScheduleService: 跳过剩余城市，等待下次调度
        end
    end

%% 用户请求流程
    Note over 客户端, Parser: 用户请求流程
    客户端 ->> Controller: 请求天气数据
    Controller ->> WeatherService: 调用getWeather方法
    WeatherService ->> WindCacheService: 委托setHourlyWindSpeed处理
    WindCacheService ->> WindCacheService: 检查6天风速缓存
    alt 缓存存在且有效
        WindCacheService -->> WeatherService: 返回缓存数据
    else 缓存不存在或需更新
        WindCacheService ->> WindCacheService: 触发异步更新
        WindCacheService -->> WeatherService: 返回现有数据

        par 异步处理
            WindCacheService ->> Parser: 异步调用解析方法
            Parser ->> 气象局网站: HTTP GET请求
            alt 解析成功
                气象局网站 -->> Parser: 返回HTML内容
                Parser -->> WindCacheService: 解析后的风速数据
                WindCacheService ->> WindCacheService: 更新6天Redis缓存
            else 解析失败
                气象局网站 -->> Parser: 异常响应/结构变动
                Parser -->> AOP: 抛出异常
                AOP ->> 企微机器人: 发送预警通知
                AOP -->> WindCacheService: 异常继续传播
                WindCacheService ->> WindCacheService: 捕获异常，执行降级策略
            end
        and 限流处理
            alt 响应限流失败
                WindCacheService ->> Queue: 加入请求队列
                JobHandler ->> Queue: 定时检查队列
                JobHandler ->> WindCacheService: 调用processQueuedRequests(使用调度限流)
                WindCacheService ->> Parser: 处理队列中的请求
            end
        end
    end
    WeatherService -->> Controller: 返回完整天气数据
    Controller -->> 客户端: JSON格式响应
```

## 核心组件

### 1. WindCacheService - 风速缓存管理

- **职责**：缓存管理、用户请求协调
- **特性**：6天缓存策略、智能验证、区分计划内外城市

### 2. WeatherScheduleService - 调度管理

- **职责**：主动调度、城市状态管理
- **特性**：6天周期、随机分布、失败重试

### 3. CityScheduleCacheService - 城市状态缓存

- **职责**：快速判断城市是否在调度计划内
- **特性**：Redis缓存、本地缓存、预加载热门城市

### 4. UnscheduledCityAlertManager - 计划外城市预警

- **职责**：预警去重、统计分析
- **特性**：频率控制、自动调度建议

### 5. CityScheduleFallbackStrategy - 降级策略

- **职责**：计划内城市异常处理
- **特性**：历史数据降级、空数据兜底

## 处理流程说明

### 用户请求流程

1. **缓存检查**：使用`CacheValidationService`智能判断缓存有效性
2. **城市分类**：区分计划内/外城市，采用不同策略
3. **计划内城市**：直接使用缓存 + 降级策略
4. **计划外城市**：同步获取数据 + 预警管理

### 主动调度流程

1. **初始化**：应用启动时随机分布调度时间
2. **定时扫描**：每10分钟检查到期城市
3. **批量更新**：协调全局限流，批量处理
4. **状态维护**：更新调度时间，处理异常

## 6天风速缓存策略

### 策略描述

```mermaid
graph TD
    A[下游服务请求] -->|检查缓存| B{6天缓存是否存在?}
    B -->|是| C{是否为当天数据且未过期?}
    C -->|是| D[返回当天缓存数据]
    C -->|否| E[返回历史缓存数据]
    E --> F[触发异步更新获取6天数据]
    B -->|否| G[返回基础数据]
    G --> H[触发异步更新]
    H -->|检查城市锁| I{城市是否已锁定?}
    I -->|是| J[直接返回]
    I -->|否| K[Redis上锁]
    K --> L{响应限流检查}
    L -->|通过| M[气象局数据获取]
    L -->|失败| N[加入请求队列]
    M -->|获取成功| O[更新6天缓存]
    M -->|获取失败| P[AOP捕获异常发送预警]
    P --> Q[异步方法异常兜底]
    Q --> R[降级策略：历史数据或空缓存]
O --> S[释放城市锁]
R --> S
N --> T[定时任务处理队列]
T --> U[WindCacheService.processQueuedRequests]
D -.->|后续请求|B
```

### 核心特性

- **6天缓存策略**：缓存有效期设置为6天，单键无日期设计
- **智能更新机制**：基于数据年龄和完整性判断
- **城市锁机制**：避免重复请求同一地区数据
- **队列处理**：限流失败时通过队列机制保证最终执行

## 关键特性

### 🚀 **性能优化**

- 6天缓存策略，减少重复请求
- 智能缓存验证，避免不必要更新
- 城市状态缓存，快速分类处理

### 🛡️ **可靠性保障**

- 多级降级策略，确保服务可用
- 预警监控机制，及时发现问题
- 重试机制，提高成功率

### 📊 **监控与统计**

- 计划内外城市请求统计
- 缓存命中率监控
- 预警频率统计

## 配置说明

使用`@Value`注解管理配置，支持默认值：

```yaml
# 主要配置项
weather:
  schedule:
    cycle-days: 6                    # 调度周期
    batch-size: 3                    # 批处理大小
    enabled: true                    # 调度开关

  wind:
    schedule-limit: 3               # 调度场景限流：3个请求/10分钟
    response-limit: 3              # 响应场景限流：3个请求/10分钟

  cache-validation:
    unscheduled-city-ttl-hours: 24   # 计划外城市缓存TTL

  unscheduled-city-alert:
    alert-interval-minutes: 60       # 预警间隔
    max-alerts-per-hour: 10          # 最大预警频率
```

## 数据库设计

### WeatherNmcCitySchedule表

```sql
CREATE TABLE `weather_nmc_city_schedule`
(
    `path`             varchar(100) NOT NULL COMMENT '城市路径',
    `next_update_time` datetime     NOT NULL COMMENT '下次更新时间',
    `status`           tinyint(4) DEFAULT '0' COMMENT '0-正常，1-暂停，2-异常',
    `retry_count`      int(11) DEFAULT '0' COMMENT '重试次数',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_path` (`path`),
    KEY                `idx_next_update_time` (`next_update_time`)
);
```

## 运维说明

### 部署要求

1. 配置XXL-JOB定时任务：`weatherScheduleJobHandler`
2. 确保Redis连接正常
3. 监控预警配置（企微机器人）

### 监控指标

- `weather.request.scheduled_city`：计划内城市请求数
- `weather.request.unscheduled_city`：计划外城市请求数
- `weather.cache.hit_rate`：缓存命中率
- `weather.fallback.triggered`：降级策略触发次数

### 故障处理

- **调度异常**：检查数据库连接和XXL-JOB配置
- **缓存问题**：检查Redis状态，必要时清理缓存
- **限流问题**：调整限流参数或增加处理频率

## 🔄 重要架构优化 (v2.1)

### 重复限流问题修复

**问题描述**：
- 调度任务在 `scanAndUpdateExpiredCities()` 和 `updateWindSpeedDataForScheduledCity()` 中存在重复限流
- 调度任务限流失败时会加入队列，造成循环依赖

**解决方案**：
1. **移除内层重复限流**：`updateWindSpeedDataForScheduledCity()` 不再进行限流检查
2. **调度任务直接执行**：调度场景在上层限流通过后，下层直接执行数据更新
3. **避免队列循环依赖**：调度任务不再加入队列，队列专门处理用户请求溢出

**优化效果**：
- ✅ 消除重复限流，提升调度效率
- ✅ 避免循环依赖，架构更清晰
- ✅ 调度任务和用户请求响应职责分离
- ✅ 遵循单一职责原则

### 限流架构分离

**调度场景限流**：
- 使用 `WEATHER_WIND_SCHEDULE_LIMIT_PREFIX` 限流key
- 配置参数：`weather.wind.schedule-limit`
- 适用于：定时调度、计划内城市更新、队列处理

**响应场景限流**：
- 使用 `WEATHER_WIND_RESPONSE_LIMIT_PREFIX` 限流key
- 配置参数：`weather.wind.response-limit`
- 适用于：用户请求触发的计划外城市数据更新

---

*文档版本：v2.1*
*最后更新：2024年12月19日*
*维护团队：后端技术组*
*重要更新：修复重复限流问题，优化调度架构*
