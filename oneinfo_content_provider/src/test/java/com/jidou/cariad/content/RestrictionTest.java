package com.jidou.cariad.content;

import com.alibaba.fastjson.JSON;
import com.jidou.cariad.content.model.vo.restriction.RestrictionRuleDTO;
import com.jidou.cariad.content.service.RestrictionService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/02/26 17:14
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = OneinfoContentProviderApplication.class)
@Slf4j
public class RestrictionTest {
    @Resource
    private RestrictionService restrictionService;

    @Test
    public void test() {
        List<RestrictionRuleDTO> restrictionCity = restrictionService.getRestrictionCity("北京", LocalDate.now());
    }

    @Test
    public void test2() {
        HashMap<String, Object> data = restrictionService.getOriginCityRules();
        String string = JSON.toJSONString(data);
    }
}
