package com.jidou.cariad.content.common.interceptor;

import com.jidou.cariad.content.common.utils.TraceIdUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * RequestIdInterceptor测试类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class RequestIdInterceptorTest {

    private RequestIdInterceptor interceptor;
    private MockHttpServletRequest request;
    private MockHttpServletResponse response;

    @BeforeEach
    void setUp() {
        interceptor = new RequestIdInterceptor();
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        // 清理MDC
        MDC.clear();
    }

    @Test
    @DisplayName("测试deviceId从请求头正确设置到MDC")
    void testDeviceIdSetToMDC() throws Exception {
        // 准备测试数据
        String testDeviceId = "test-device-123";
        request.addHeader(RequestIdInterceptor.DEVICE_ID_HEADER, testDeviceId);

        // 执行拦截器
        boolean result = interceptor.preHandle(request, response, null);

        // 验证结果
        assertThat(result).isTrue();
        assertThat(MDC.get(TraceIdUtils.DEVICE_ID)).isEqualTo(testDeviceId);
    }

    @Test
    @DisplayName("测试deviceId为空时不设置MDC")
    void testEmptyDeviceIdNotSetToMDC() throws Exception {
        // 不设置deviceId请求头

        // 执行拦截器
        boolean result = interceptor.preHandle(request, response, null);

        // 验证结果
        assertThat(result).isTrue();
        assertThat(MDC.get(TraceIdUtils.DEVICE_ID)).isNull();
    }

    @Test
    @DisplayName("测试requestId和deviceId同时处理")
    void testRequestIdAndDeviceIdTogether() throws Exception {
        // 准备测试数据
        String testRequestId = "test-request-123";
        String testDeviceId = "test-device-456";
        request.addHeader(RequestIdInterceptor.REQUEST_ID_HEADER, testRequestId);
        request.addHeader(RequestIdInterceptor.DEVICE_ID_HEADER, testDeviceId);

        // 执行拦截器
        boolean result = interceptor.preHandle(request, response, null);

        // 验证结果
        assertThat(result).isTrue();
        assertThat(MDC.get(RequestIdInterceptor.REQUEST_ID)).isEqualTo(testRequestId);
        assertThat(MDC.get(TraceIdUtils.DEVICE_ID)).isEqualTo(testDeviceId);
        assertThat(response.getHeader(RequestIdInterceptor.REQUEST_ID_HEADER)).isEqualTo(testRequestId);
    }

    @Test
    @DisplayName("测试afterCompletion清理MDC")
    void testAfterCompletionCleansMDC() throws Exception {
        // 准备测试数据
        String testDeviceId = "test-device-789";
        request.addHeader(RequestIdInterceptor.DEVICE_ID_HEADER, testDeviceId);

        // 执行拦截器
        interceptor.preHandle(request, response, null);
        
        // 验证设置成功
        assertThat(MDC.get(TraceIdUtils.DEVICE_ID)).isEqualTo(testDeviceId);

        // 执行清理
        interceptor.afterCompletion(request, response, null, null);

        // 验证清理成功
        assertThat(MDC.get(TraceIdUtils.DEVICE_ID)).isNull();
        assertThat(MDC.get(RequestIdInterceptor.REQUEST_ID)).isNull();
    }
}
