---
description: Java开发规范指南
globs: *.java
alwaysApply: false
---
# Java开发规范

> file_patterns: ["*.java"]
> description: Java开发的核心规范和关键实践

## 相关规范
> required: true

本规范与以下规范配合使用：
- `code_quality_standards.mdc` - 基于Checkstyle的代码质量标准
- `testing_standards.mdc` - 测试规范标准
- `incremental_changes.mdc` - 渐进式修改指南

详细的代码格式、命名规范、文件长度等质量标准请参考 `code_quality_standards.mdc`。

## 核心原则
> required: true

### KISS原则
```java
// ✅ 推荐：简单直接的实现
@Service
@RequiredArgsConstructor
public class UserService {
    private final UserRepository userRepository;
    
    public Optional<User> getUser(Long userId) {
        return userRepository.findById(userId);
    }
    
    public boolean validateUser(User user) {
        return user != null && user.getId() != null && user.getName() != null;
    }
}

// ❌ 避免：过度设计
public interface UserServiceFactory {
    UserService createService(String type);
}

@Component
public class UserServiceFactoryImpl implements UserServiceFactory {
    @Override
    public UserService createService(String type) {
        return switch (type) {
            case "admin" -> new AdminUserService(new AdminValidator(), new AdminRepository());
            case "guest" -> new GuestUserService(new GuestValidator(), new GuestRepository());
            default -> throw new IllegalArgumentException("Unknown type: " + type);
        };
    }
    // 过度的抽象和工厂模式
}
```

### 显式优于隐式
```java
// ✅ 推荐：明确的参数验证和返回值
@Service
public class UserService {
    public Optional<User> getUserById(Long userId) {
        Objects.requireNonNull(userId, "用户ID不能为空");
        return userRepository.findById(userId);
    }
}

// ❌ 避免：隐含的类型转换和返回值
@Service
public class UserService {
    public User getUser(String id) {
        // 隐式类型转换，可能抛出异常
        return userRepository.findById(Long.parseLong(id))
            .orElse(null);  // 可能返回null
    }
}
```

### 组合优于继承
```java
// ✅ 推荐：使用组合实现功能
@Service
@RequiredArgsConstructor
public class DataProcessor {
    private final DataReader reader;
    private final DataValidator validator;
    
    public ProcessResult process(String dataPath) {
        Data data = reader.read(dataPath);
        return validator.validate(data);
    }
}

// ❌ 避免：过度继承
public abstract class BaseProcessor {
    abstract void read();
    abstract void validate();
    abstract void process();
}

public class JsonProcessor extends BaseProcessor {  // 继承导致强耦合
    @Override
    void read() {}
    @Override
    void validate() {}
    @Override
    void process() {}
}
```

### 依赖注入优于直接实例化
```java
// ✅ 推荐：依赖注入
@Service
@RequiredArgsConstructor
public class ImageProcessor {
    private final ProcessorConfig config;
    
    public BufferedImage processImage(BufferedImage image) {
        return Thumbnails.of(image)
            .size(config.getTargetWidth(), config.getTargetHeight())
            .asBufferedImage();
    }
}

// ❌ 避免：直接实例化
public class ImageProcessor {
    private static final ProcessorConfig CONFIG = new ProcessorConfig();  // 直接实例化
    
    public BufferedImage processImage(BufferedImage image) {
        return Thumbnails.of(image)
            .size(CONFIG.getTargetWidth(), CONFIG.getTargetHeight())
            .asBufferedImage();
    }
}
```

### 代码质量
```java
// ✅ 推荐：清晰的方法签名和文档
/**
 * 计算平均分数
 * @param scores 分数列表
 * @return 平均分数，列表为空时返回0
 */
public double calculateAverage(List<Double> scores) {
    return scores.stream()
        .mapToDouble(Double::doubleValue)
        .average()
        .orElse(0.0);
}

// ❌ 避免：含糊不清的实现
public double calc(List l) {
    return l.isEmpty() ? 0 : l.stream().mapToDouble(v -> (double)v).average().getAsDouble();
}
```

### 错误处理
```java
// ✅ 推荐：明确的错误处理
public class DataProcessor {
    public ProcessResult processData(Map<String, Object> data) {
        if (data == null) {
            throw new IllegalArgumentException("数据不能为空");
        }
        if (data.isEmpty()) {
            throw new IllegalArgumentException("数据不能为空Map");
        }
        return new ProcessResult(transform(data));
    }
}

// ❌ 避免：吞掉异常
public class DataProcessor {
    public Map<String, Object> process(Map<String, Object> data) {
        try {
            return transform(data);
        } catch (Exception e) {
            return new HashMap<>();  // 吞掉异常
        }
    }
}
```

## 复杂系统开发经验教训
> required: true

### 系统性思维的重要性

在复杂系统优化中，最常见也是最致命的错误是**缺乏系统性分析**。

```java
// ❌ 错误做法：局部修复编译错误
public interface DataProcessor {
    // 在接口内部定义DTO类 - 违反单一职责
    class RequestInfo {
        private String data;
        // ... 
    }
    
    void process(RequestInfo request);
}

// ❌ 错误做法：使用通配符导入
import com.company.service.*;  // 违反导入规范

// ❌ 错误做法：混合接口和实现
@Component  // 应该是接口+实现的结构
public class DataProcessor {
    // 实现代码
}
```

```java
// ✅ 正确做法：系统性设计
// 1. 统一的DTO管理
package com.company.dto;
public class RequestInfo {
    private String data;
    // ...
}

// 2. 清晰的接口定义
package com.company.service;
import com.company.dto.RequestInfo;  // 明确导入

public interface DataProcessor {
    void process(RequestInfo request);
}

// 3. 独立的实现类
package com.company.service.impl;
import com.company.dto.RequestInfo;
import com.company.service.DataProcessor;

@Service
@RequiredArgsConstructor
public class DataProcessorImpl implements DataProcessor {
    @Override
    public void process(RequestInfo request) {
        // 实现逻辑
    }
}
```

### 数据类型一致性原则

**问题根源**：在不同地方定义相同概念的类型，导致类型不匹配

```java
// ❌ 违反一致性：多处定义相同概念
public interface AlertManager {
    // 接口内部定义
    class RequestInfo {
        private LocalDateTime time;
    }
    void sendAlert(RequestInfo request);
}

public class DataService {
    // 另一处定义
    class RequestInfo {
        private String timestamp;
    }
}
```

```java
// ✅ 遵循一致性：统一类型管理
// dto/RequestInfo.java - 唯一定义处
@Data
@Builder
public class RequestInfo {
    private LocalDateTime requestTime;
    private String remoteIp;
    private String userAgent;
    private long responseTime;
}

// 所有服务统一使用
import com.company.dto.RequestInfo;

public interface AlertManager {
    void sendAlert(String locationPath, RequestInfo request);
}

public interface DataService {
    void processRequest(RequestInfo request);
}
```

### 项目结构规范遵循

**关键原则**：严格按照项目结构规范组织代码，避免违反分层架构

```java
// ❌ 违反结构规范
src/main/java/com/company/
├── service/
│   ├── UserService.java           // 接口
│   └── UserServiceImpl.java       // ❌ 实现类放错位置
└── dto/
    └── UserInfo.java

// ❌ 违反结构规范：接口内定义DTO
public interface UserService {
    class UserInfo {  // ❌ DTO应该独立管理
        // ...
    }
}
```

```java
// ✅ 遵循结构规范
src/main/java/com/company/
├── service/
│   └── UserService.java           // 接口定义
├── service/impl/
│   └── UserServiceImpl.java       // ✅ 实现类正确位置
└── dto/
    └── UserInfo.java               // ✅ DTO独立管理

// ✅ 遵循结构规范：清晰的分层
public interface UserService {     // 仅定义契约
    Optional<UserInfo> getUser(Long id);
}

@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {  // 实现具体逻辑
    private final UserRepository userRepository;
    
    @Override
    public Optional<UserInfo> getUser(Long id) {
        return userRepository.findById(id)
            .map(UserInfo::from);
    }
}
```

### 导入规范的严格遵循

**核心要求**：明确导入，避免通配符，按类型分组

```java
// ❌ 违反导入规范
import java.util.*;                    // 通配符导入
import com.company.service.*;          // 通配符导入
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;        // 导入顺序混乱
```

```java
// ✅ 遵循导入规范
// 1. Java核心包
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

// 2. 第三方库包
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

// 3. 项目内部包
import com.company.dto.RequestInfo;
import com.company.service.AlertManager;
import com.company.service.DataProcessor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
```

### 配置管理最佳实践

**原则**：轻量化配置，避免过度配置文件

```java
// ❌ 错误做法：创建不必要的配置文件
// application-feature.yml
feature:
  cache:
    enabled: true
    ttl: 60
  alert:
    enabled: true
    interval: 30
```

```java
// ✅ 正确做法：使用@Value注解
@Service
@RequiredArgsConstructor
@Slf4j
public class FeatureService {
    
    @Value("${feature.cache.enabled:true}")
    private boolean cacheEnabled;
    
    @Value("${feature.cache.ttl:60}")
    private int cacheTtl;
    
    @Value("${feature.alert.enabled:true}")
    private boolean alertEnabled;
    
    @Value("${feature.alert.interval:30}")
    private int alertInterval;
    
    // 业务逻辑...
}
```

### 问题诊断和修复策略

**系统性问题解决流程**：

1. **整体分析优于局部修复**
```java
// ❌ 局部修复思维
// 看到编译错误 -> 直接修改 -> 引入新问题 -> 继续修改 -> 问题累积

// ✅ 系统性分析思维
// 1. 分析架构设计是否合理
// 2. 检查是否遵循项目规范
// 3. 统一解决所有相关问题
// 4. 验证解决方案的完整性
```

2. **根因分析原则**
```java
// ❌ 症状导向：只看编译错误信息
"RequestInfo类型不匹配" -> 改类型 -> 新的类型错误

// ✅ 根因导向：分析设计问题
"为什么有两个RequestInfo类型？" -> 
"违反了数据类型统一原则" -> 
"重新设计类型管理策略" -> 
"系统性解决所有类型问题"
```

3. **验证完整性检查**
```java
// 修复完成后的检查清单
- [ ] 是否遵循项目结构规范？
- [ ] 是否违反了导入规范？
- [ ] 是否有重复定义的类型？
- [ ] 是否正确使用了依赖注入？
- [ ] 是否添加了必要的文档？
```

### 团队协作中的规范意识

**关键认知**：个人代码质量直接影响团队效率

```java
// ❌ 缺乏规范意识的代码
public interface DataService {
    // 1. 违反命名规范
    void DoSomething(String data);
    
    // 2. 在接口定义DTO
    class MyData {
        public String value;  // 3. 违反封装性
    }
    
    // 4. 缺乏文档
    List process(Object input);
}
```

```java
// ✅ 具备规范意识的代码
/**
 * 数据处理服务
 * 负责处理业务数据的核心逻辑
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface DataService {
    
    /**
     * 处理业务数据
     * 
     * @param request 处理请求
     * @return 处理结果
     * @throws IllegalArgumentException 当请求参数无效时
     */
    ProcessResult processData(DataRequest request);
    
    /**
     * 验证数据完整性
     * 
     * @param data 待验证数据
     * @return true-验证通过，false-验证失败
     */
    boolean validateData(DataRequest data);
}
```

## 代码组织
> required: true

### 包结构
```java
// 标准包结构
package com.company.project;

// 核心包导入
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

// 第三方库导入
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;

// 项目内部导入
import com.company.project.domain.User;
import com.company.project.repository.UserRepository;
```

### 类设计
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessorConfig {
    private String modelPath;
    private int batchSize;
    private int maxRetries = 3;
}

@Service
@RequiredArgsConstructor
public class DataProcessor {
    private final ProcessorConfig config;
    private final DataValidator validator;
    
    @Transactional(readOnly = true)
    public List<ProcessResult> process(List<Data> dataList) {
        return dataList.stream()
            .map(this::processItem)
            .collect(Collectors.toList());
    }
    
    private ProcessResult processItem(Data item) {
        validator.validate(item);
        return transform(item);
    }
}
```

### 文件格式
```java
// ✅ 推荐：每个文件末尾添加一个空行
public class DataService {
    // 类实现
}
// 此处有一个空行

// ❌ 避免：文件末尾没有空行
public class DataService {
    // 类实现
}
```

## 性能优化
> required: true

### 关键优化
```java
// ✅ 推荐：合理使用缓存
@Service
public class ConfigService {
    @Cacheable(value = "configs", key = "#key")
    public String getConfig(String key) {
        return loadConfig().get(key);
    }
}

// ✅ 推荐：流式处理大数据
@Service
public class LargeFileProcessor {
    public void processLargeFile(Path filePath) {
        try (Stream<String> lines = Files.lines(filePath)) {
            lines.forEach(this::processLine);
        }
    }
}
```

## 测试规范
> required: true

### 单元测试
```java
@SpringBootTest
class DataProcessorTest {
    @Autowired
    private DataProcessor processor;
    
    @MockBean
    private DataValidator validator;
    
    @Test
    @DisplayName("测试数据处理器的核心功能")
    void testDataProcessor() {
        // 准备测试数据
        ProcessorConfig config = ProcessorConfig.builder()
            .modelPath("./models")
            .batchSize(32)
            .build();
        
        List<Data> testData = List.of(new Data(1L, "test"));
        
        // 验证核心功能
        List<ProcessResult> results = processor.process(testData);
        assertThat(results)
            .isNotEmpty()
            .first()
            .matches(r -> "success".equals(r.getStatus()));
        
        // 验证错误处理
        assertThatThrownBy(() -> processor.process(null))
            .isInstanceOf(IllegalArgumentException.class);
    }
}
```

## 检查清单
> required: true

### 代码规范检查
- [ ] 是否遵循KISS原则，避免过度设计
- [ ] 是否使用显式代码，避免隐式行为
- [ ] 是否优先使用组合而非继承
- [ ] 是否使用依赖注入而非直接实例化
- [ ] 是否添加了完整的JavaDoc注释

### 设计原则检查
- [ ] 是否职责单一，功能内聚
- [ ] 是否正确处理异常情况
- [ ] 是否遵循标准的包结构
- [ ] 是否定义了清晰的接口
- [ ] 是否使用了合适的设计模式

### 系统性设计检查
- [ ] 是否从整体架构角度分析问题
- [ ] 是否严格遵循项目结构规范
- [ ] 是否统一管理数据类型定义
- [ ] 是否正确使用导入规范
- [ ] 是否避免了重复定义和类型冲突

### 性能检查
- [ ] 是否合理使用缓存
- [ ] 是否正确处理大数据场景
- [ ] 是否优化了数据库查询
- [ ] 是否避免了内存泄漏
- [ ] 是否使用了适当的并发控制

### 测试检查
- [ ] 是否编写了单元测试
- [ ] 是否包含集成测试
- [ ] 是否测试了边界条件
- [ ] 是否测试了异常情况



- [ ] 是否使用了测试替身（Mock/Stub） 